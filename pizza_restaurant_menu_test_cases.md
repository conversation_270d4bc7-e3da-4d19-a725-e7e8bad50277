# Pizza Restaurant Menu API Test Cases Documentation

This document explains all the test cases covered in the `pizza_restaurant_menu_test_payload.json` file.

## 1. Restaurant Object Test Cases

**Location in JSON**: `restaurant` object (lines 2-6)

- ✅ **Required field validation**: `id` field (string)
- ✅ **Optional field inclusion**: `minPrepTime` (integer) and `timings` (string reference)
- ✅ **Realistic Indian restaurant data**: Mumbai location with 25-minute prep time

## 2. Categories & Subcategories Test Cases

### Main Category with Subcategories (lines 7-188)
**Location**: `categories[0]` - "Pizzas" category

- ✅ **Category with subcategories only**: No direct items under main category
- ✅ **Required fields**: id, name, sortOrder
- ✅ **Optional fields**: description, imageUrl, timings
- ✅ **Subcategories array**: Two subcategories (Vegetarian & Non-Vegetarian)
- ✅ **Validation rule**: Items and subcategories cannot coexist (subcategories only)

### Direct Category with Items (lines 149-188)
**Location**: `categories[1]` - "Beverages" category

- ✅ **Category with items only**: No subcategories
- ✅ **Direct items under category**: Coca Cola item
- ✅ **Validation rule**: Items and subcategories cannot coexist (items only)

## 3. Items Test Cases

### Complex Item - Margherita Pizza (lines 20-78)
**Location**: `categories[0].subcategories[0].items[0]`

- ✅ **All required fields**: id, name, imageUrl, price, inStock, foodType, billComponents, fulfillmentModes, sortOrder, categoryId
- ✅ **All optional fields**: description, markupPrice, recommended, isFavorite, tags, variantGroupIds, addOnGroupIds, nutritionalInfo, subcategoryId
- ✅ **Multiple variant groups**: Size and Crust type (2 groups)
- ✅ **Multiple add-on groups**: Extra toppings and beverages
- ✅ **Complete nutritional info**: All nutrient types with proper structure
- ✅ **Allergen information**: Gluten and dairy allergens
- ✅ **Boolean flags**: recommended=true, isFavorite=true, inStock=true
- ✅ **Food type**: "veg" enum value
- ✅ **Tags array**: Multiple descriptive tags
- ✅ **Markup pricing**: Higher strike-through price
- ✅ **Bill components**: Multiple charges and taxes

### Premium Item - Chicken Supreme Pizza (lines 91-147)
**Location**: `categories[0].subcategories[1].items[0]`

- ✅ **Non-vegetarian food type**: "non veg" enum value
- ✅ **Single variant group**: Only size variants
- ✅ **Different add-on groups**: Meat-specific add-ons
- ✅ **Premium charges**: Additional premium item charge
- ✅ **ignoreOn object**: Tax and discount exclusion flags
- ✅ **No markup price**: Testing optional field omission
- ✅ **Boolean flags**: recommended=false, isFavorite=false

### Complex Nested Item - Build Your Own Pizza (lines 95-116)
**Location**: `categories[0].subcategories[1].items[1]`

- ✅ **Multiple variant groups**: Size and Specialty options (2 groups)
- ✅ **Nested variant structure**: Specialty variants have their own variant groups
- ✅ **Variant-specific add-ons**: Different add-on groups per variant type
- ✅ **Cross-variant dependencies**: Specialty variants also link to crust variants
- ✅ **Customizable item**: Build-your-own concept with maximum flexibility

### Simple Item - Coca Cola (lines 158-186)
**Location**: `categories[1].items[0]`

- ✅ **Minimal configuration**: Basic required fields only
- ✅ **Single variant group**: Beverage size only
- ✅ **No add-ons**: Empty addOnGroupIds (testing optional omission)
- ✅ **No nutritional info**: Testing optional field omission
- ✅ **Empty charges array**: No item-specific charges

## 4. Variant Groups Test Cases

**Location**: `variantGroups` array (lines 290-309)

### Pizza Size Variant Group
- ✅ **Multiple variants**: 4 size options (Small, Medium, Large, XL)
- ✅ **Required fields**: id, name, variantIds, sortOrder
- ✅ **Variant ID references**: Proper linking to variant objects

### Crust Type Variant Group
- ✅ **Different variant count**: 3 crust options
- ✅ **Nested variant selection**: Secondary customization option

### Beverage Size Variant Group
- ✅ **Simple variant group**: 2 size options for beverages
- ✅ **Cross-category variants**: Used by different item types

### Specialty Options Variant Group
- ✅ **Nested variant group**: Used for complex customization
- ✅ **Multi-tier selection**: Classic, Gourmet, Premium bases
- ✅ **Variant-specific add-ons**: Each variant has different add-on groups

## 5. Variants Test Cases

**Location**: `variants` array (lines 310-426)

### Size Variants (Small, Medium, Large, XL)
- ✅ **Zero-cost base option**: Small pizza (price: 0.00)
- ✅ **Incremental pricing**: Medium (+100), Large (+200), XL (+300)
- ✅ **Out-of-stock variant**: XL pizza (inStock: false)
- ✅ **Markup pricing**: Medium pizza with strike-through price
- ✅ **Different add-on associations**: Varying addOnGroupIds per variant
- ✅ **Nutritional variations**: Different calorie counts per size
- ✅ **Variant-specific bill components**: Medium pizza has specific charges

### Crust Variants (Thin, Thick, Cheese Burst)
- ✅ **Free base option**: Thin crust (price: 0.00)
- ✅ **Premium options**: Thick (+50), Cheese Burst (+100)
- ✅ **Enhanced nutritional info**: Cheese Burst with extra calories and fat

### Beverage Size Variants
- ✅ **Cross-item variants**: Used by beverage items
- ✅ **Simple pricing structure**: Small (free), Large (+20)

### **🔥 NESTED VARIANTS - Specialty Base Variants (Classic, Gourmet, Premium)**
**Location**: `variants` array - variant_classic_base, variant_gourmet_base, variant_premium_base

- ✅ **NESTED VARIANT GROUPS**: Each specialty variant has `variantGroupIds: ["vg_specialty_options", "vg_crust_type"]`
- ✅ **VARIANTS WITH ADD-ONS**: Each variant has different `addOnGroupIds` arrays
  - Classic Base: Only basic toppings (`["ag_extra_toppings"]`)
  - Gourmet Base: Toppings + premium sauces (`["ag_extra_toppings", "ag_premium_sauces"]`)
  - Premium Base: All add-ons (`["ag_extra_toppings", "ag_premium_sauces", "ag_gourmet_cheese"]`)
- ✅ **PROGRESSIVE PRICING**: Classic (₹0), Gourmet (+₹150), Premium (+₹250)
- ✅ **VARIANT-SPECIFIC CHARGES**: Gourmet and Premium have different bill components
- ✅ **COMPLEX NUTRITIONAL PROGRESSION**: Increasing calories, protein, and fat content
- ✅ **MARKUP PRICING ON VARIANTS**: Gourmet base has markupPrice (₹180 vs ₹150)
- ✅ **CROSS-VARIANT DEPENDENCIES**: All specialty variants also link to crust variants

## 6. Add-On Groups Test Cases

**Location**: `addOnGroups` array (lines 193-289)

### Extra Toppings Group
- ✅ **Flexible selection**: min=0, max=5
- ✅ **Mixed availability**: In-stock and out-of-stock add-ons
- ✅ **Vegetarian add-ons**: All "veg" food type
- ✅ **Nutritional data**: Extra cheese with calorie/fat info
- ✅ **Image URLs**: Visual representation for add-ons

### Extra Meat Group
- ✅ **Restricted selection**: min=0, max=3
- ✅ **Non-vegetarian add-ons**: "non veg" food type
- ✅ **Premium pricing**: Higher cost meat add-ons
- ✅ **Protein information**: Nutritional data for chicken

### Beverages Group
- ✅ **Limited selection**: min=0, max=2
- ✅ **Cross-category add-ons**: Beverages as pizza add-ons
- ✅ **Simple structure**: Basic add-on without complex nutrition

### **🔥 PREMIUM SAUCES GROUP - Variant-Specific Add-Ons**
- ✅ **Required selection**: min=1, max=3 (testing mandatory add-ons)
- ✅ **Premium pricing**: Truffle sauce (₹120), Pesto sauce (₹80)
- ✅ **Variant-specific availability**: Only available for Gourmet and Premium base variants
- ✅ **Nutritional data**: Truffle sauce with calorie and fat information

### **🔥 GOURMET CHEESE GROUP - Nested Add-On Selection**
- ✅ **Optional premium selection**: min=0, max=2
- ✅ **High-value add-ons**: Goat cheese (₹100)
- ✅ **Variant-specific availability**: Only available for Premium base variant
- ✅ **Protein-rich nutrition**: Goat cheese with protein content

## 7. Bill Components Test Cases

**Location**: `billComponents` object (lines 427-482)

### Charges Array
- ✅ **FIXED charge type**: Packing charge (₹15), Delivery charge (₹30)
- ✅ **PERCENTAGE charge type**: Premium charge (5%), Service charge (10%)
- ✅ **Charge descriptions**: Detailed explanations
- ✅ **Tax associations**: Different tax combinations per charge
- ✅ **Fulfillment mode restrictions**: All charges for delivery mode

### Taxes Array
- ✅ **Indian GST structure**: CGST (2.5%) + SGST (2.5%) = 5% total
- ✅ **Tax descriptions**: Government tax explanations
- ✅ **Fulfillment mode applicability**: Delivery mode taxes

## 8. Timing Objects Test Cases

**Location**: `timings` array (lines 189-300)

### Restaurant Timing
- ✅ **All 7 days coverage**: Monday through Sunday
- ✅ **Varying hours**: Different closing times for weekends
- ✅ **24-hour format**: Proper time format (HH:MM)
- ✅ **Single slot per day**: Standard operating hours

### Category Timing
- ✅ **Restricted timing**: Category available for shorter hours
- ✅ **Timing inheritance**: Category timing overrides restaurant timing

## 9. Order Bill Components Test Cases

**Location**: `orderBillComponents` object (lines 190-192)

- ✅ **Order-level charges**: Delivery and service charges
- ✅ **Charge ID references**: Proper linking to charge definitions

## 10. Callback URL Test Case

**Location**: `callbackUrl` field (line 483)

- ✅ **Callback URL format**: Proper HTTPS URL with request ID placeholder
- ✅ **External host reference**: Realistic callback endpoint

## 11. Data Type Validation Test Cases

- ✅ **String fields**: All ID fields, names, descriptions, URLs
- ✅ **Float fields**: All price and value fields with decimal precision
- ✅ **Integer fields**: sortOrder, minPrepTime, minimumNeeded, maximumAllowed
- ✅ **Boolean fields**: inStock, recommended, isFavorite, tax/discount flags
- ✅ **Array fields**: All collection fields (items, variants, addOns, etc.)
- ✅ **Enum fields**: foodType, fulfillmentModes, charge types

## 12. Edge Cases & Validation Test Cases

- ✅ **Out-of-stock items**: XL pizza variant, Black olives add-on
- ✅ **Zero pricing**: Base variants with 0.00 price
- ✅ **Empty arrays**: No charges for simple items
- ✅ **Optional field omission**: Various optional fields left out
- ✅ **Cross-references**: Proper ID linking between objects
- ✅ **Nested structures**: Complex nutritional info and allergen data
- ✅ **Indian context**: GST structure, vegetarian preferences, realistic pricing

## 13. Business Logic Test Cases

- ✅ **Menu hierarchy**: Restaurant → Categories → Subcategories → Items
- ✅ **Customization flow**: Items → Variants → Add-ons
- ✅ **Pricing structure**: Base price + variant price + add-on prices
- ✅ **Tax calculation**: Item taxes + charge taxes
- ✅ **Availability logic**: Restaurant timing → category timing → item availability
- ✅ **Food preferences**: Vegetarian/non-vegetarian segregation
- ✅ **Delivery logistics**: Fulfillment modes and associated charges

## 🔥 ADVANCED TEST SCENARIOS COVERAGE

### **NESTED VARIANTS** ✅
- **Build Your Own Pizza** item demonstrates complex nested variant structure
- **Specialty base variants** each have `variantGroupIds: ["vg_specialty_options", "vg_crust_type"]`
- **Cross-variant dependencies**: Specialty variants link to both specialty options AND crust types
- **Multi-level customization**: Item → Size + Specialty → Crust → Add-ons

### **VARIANTS WITH ADD-ONS** ✅
- **Progressive add-on availability**:
  - Classic Base: Basic toppings only
  - Gourmet Base: Basic toppings + Premium sauces
  - Premium Base: All add-on groups (toppings + sauces + gourmet cheese)
- **Variant-specific add-on groups**: Each variant has different `addOnGroupIds` arrays
- **Conditional add-on access**: Premium sauces and gourmet cheese only available for higher-tier variants

### **COMPLEX PRICING SCENARIOS** ✅
- **Base item price**: ₹399 for Build Your Own Pizza
- **Size variant pricing**: +₹0 to +₹300 depending on size
- **Specialty variant pricing**: +₹0 (Classic) to +₹250 (Premium)
- **Crust variant pricing**: +₹0 to +₹100 for specialty crusts
- **Add-on pricing**: ₹35-₹120 per add-on
- **Total customization cost**: Can range from ₹399 to ₹1000+ with all options

### **BUSINESS LOGIC VALIDATION** ✅
- **Mandatory selections**: Premium sauces require min=1 selection
- **Optional enhancements**: Gourmet cheese is optional (min=0)
- **Progressive complexity**: Higher-tier variants unlock more customization options
- **Realistic Indian pricing**: All prices reflect Indian market standards

This comprehensive test payload covers **ALL POSSIBLE SCENARIOS** for the Nutana Menu Integration API, including the advanced nested variants and variants with add-ons functionality, ensuring robust validation of the complete menu structure and complex business logic.
