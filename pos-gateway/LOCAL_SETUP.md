# Local MongoDB Setup for POS Gateway

This guide explains how to set up local MongoDB with replica set support for menu and order transactions in the POS Gateway.

## Prerequisites

- Docker and Docker Compose installed
- Go 1.22+ installed
- Make utility

## Quick Start

1. **Start Local Environment**
   ```bash
   make start-local-env
   ```
   This will start MongoDB with replica set and Kafka using Docker Compose.

2. **Run POS Gateway with Local Config**
   ```bash
   make build-run-server-local
   ```
   This builds and runs the server using the local configuration.

## Manual Setup

### 1. Start MongoDB and Kafka Services

```bash
docker-compose up -d mongodb mongo-setup kafka zookeeper
```

### 2. Verify MongoDB Replica Set

```bash
docker-compose exec mongodb mongosh --eval "rs.status()"
```

You should see the replica set status with `rapidoReplSetv4` as the set name.

### 3. Verify Collections

```bash
docker-compose exec mongodb mongosh restaurants --eval "show collections"
```

You should see `pos_menu` and `pos_order` collections.

## Configuration

The local setup uses `config/local.yaml` which configures:

- **MongoDB**: `localhost:27017` with replica set `rapidoReplSetv4`
- **Kafka**: `localhost:9092`
- **Database**: `restaurants`
- **No Authentication**: For local development

## Transaction Support

The local MongoDB setup includes:

- **Replica Set**: Required for MongoDB transactions
- **Collections**: `pos_menu` and `pos_order` with appropriate indexes
- **Transaction Support**: Enabled for menu and order operations

## Useful Commands

```bash
# Start local environment
make start-local-env

# Stop local environment
make stop-local-env

# View MongoDB logs
make logs-mongo

# View Kafka logs
make logs-kafka

# Run server with local config
make build-run-server-local

# Connect to MongoDB shell
docker-compose exec mongodb mongosh restaurants
```

## Testing Transactions

You can test the transaction functionality by:

1. Starting the local environment
2. Running the POS Gateway server
3. Making API calls to create/update menus and orders
4. Verifying data in MongoDB collections

## Troubleshooting

### MongoDB Connection Issues

If you see connection errors:

1. Ensure Docker is running
2. Check if MongoDB container is healthy: `docker-compose ps`
3. Verify replica set status: `docker-compose exec mongodb mongosh --eval "rs.status()"`

### Transaction Errors

If transactions fail:

1. Ensure replica set is properly initialized
2. Check MongoDB logs: `make logs-mongo`
3. Verify the connection string includes `replicaSet=rapidoReplSetv4`

### Port Conflicts

If ports 27017 or 9092 are in use:

1. Stop conflicting services
2. Or modify `docker-compose.yml` to use different ports
3. Update `config/local.yaml` accordingly

## File Structure

```
pos-gateway/
├── scripts/
│   ├── mongo-init-simple.js      # MongoDB initialization
│   ├── setup-replica-set.sh      # Replica set setup
│   └── start-local-mongo.sh      # Local environment startup
├── config/
│   ├── local.yaml                # Local development config
│   ├── application.yaml          # Production config
│   └── test.yaml                 # Test config
└── docker-compose.yml            # Docker services
```
