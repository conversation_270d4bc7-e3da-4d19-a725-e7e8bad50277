log:
  level: debug

server:
  host: localhost
  port: 8081

environment: local

mongo:
  hosts: localhost:27017
  user: ""
  password: ""
  database: restaurants
  authSource: ""
  replicaSet: rapidoReplSetv4
  appName: pos-gateway

kafka:
  bootstrapServers: localhost:9092
  groupID: pos-gateway
  autoOffsetReset: latest
  enableAutoCommit: true
  sessionTimeout: 10000

restroworks:
  baseURL: https://api.restroworks.com

freshmenu:
  baseURL: https://api.freshmenu.com

profilingEnabled: false
