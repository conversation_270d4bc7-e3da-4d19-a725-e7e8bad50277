{"validation_test_cases": {"valid_cases": {"1_minimal_valid_order": {"description": "Minimal valid order with all required fields", "payload": {"orderInfo": {"orderId": "ORD_MIN_001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, "2_valid_with_all_payment_modes": {"description": "Valid orders with different payment modes", "payloads": [{"description": "Cash payment", "orderInfo": {"orderId": "ORD_CASH_001", "restId": "REST1001", "status": "placed", "subTotal": 200.0, "totalPackingCharge": 10.0, "totalTaxes": 20.0, "createdAt": 1717046400, "total": 230.0}, "payment": {"mode": "cash", "status": "paid", "amountPaid": 230.0}, "customer": {"firstName": "Cash", "lastName": "Customer"}, "item": [{"itemId": "ITEM001", "name": "Cash Item", "quantity": 1, "unitPrice": 200.0, "taxes": [{"title": "VAT", "value": 20.0, "percentage": 10.0, "liability_on": "restaurant"}], "charges": [{"title": "Handling", "value": 10.0, "liability_on": "restaurant"}]}]}, {"description": "Card payment", "orderInfo": {"orderId": "ORD_CARD_001", "restId": "REST1001", "status": "placed", "subTotal": 300.0, "totalPackingCharge": 15.0, "totalTaxes": 30.0, "createdAt": 1717046400, "total": 345.0}, "payment": {"mode": "card", "status": "paid", "amountPaid": 345.0}, "customer": {"firstName": "Card", "lastName": "Customer"}, "item": [{"itemId": "ITEM002", "name": "Card Item", "quantity": 1, "unitPrice": 300.0, "taxes": [{"title": "Service Tax", "value": 30.0, "percentage": 10.0, "liability_on": "aggregator"}], "charges": [{"title": "Processing Fee", "value": 15.0, "liability_on": "aggregator"}]}]}, {"description": "UPI payment", "orderInfo": {"orderId": "ORD_UPI_001", "restId": "REST1001", "status": "placed", "subTotal": 150.0, "totalPackingCharge": 8.0, "totalTaxes": 15.0, "createdAt": 1717046400, "total": 173.0}, "payment": {"mode": "upi", "status": "paid", "amountPaid": 173.0}, "customer": {"firstName": "UPI", "lastName": "Customer"}, "item": [{"itemId": "ITEM003", "name": "UPI Item", "quantity": 1, "unitPrice": 150.0, "taxes": [{"title": "Digital Tax", "value": 15.0, "percentage": 10.0, "liability_on": "restaurant"}], "charges": [{"title": "Digital Fee", "value": 8.0, "liability_on": "restaurant"}]}]}]}, "3_valid_delivery_modes": {"description": "Valid orders with different delivery modes", "payloads": [{"description": "Self delivery", "orderInfo": {"orderId": "ORD_SELF_001", "restId": "REST1001", "status": "placed", "subTotal": 250.0, "totalPackingCharge": 12.0, "totalTaxes": 25.0, "createdAt": 1717046400, "total": 287.0, "deliveryMode": "self_delivery"}, "payment": {"mode": "online", "status": "paid", "amountPaid": 287.0}, "customer": {"firstName": "Self", "lastName": "Delivery"}, "item": [{"itemId": "ITEM004", "name": "Self Delivery Item", "quantity": 1, "unitPrice": 250.0, "taxes": [{"title": "Tax", "value": 25.0, "percentage": 10.0, "liability_on": "restaurant"}], "charges": [{"title": "Prep Charge", "value": 12.0, "liability_on": "restaurant"}]}]}, {"description": "Platform delivery", "orderInfo": {"orderId": "ORD_DELIVERY_001", "restId": "REST1001", "status": "placed", "subTotal": 180.0, "totalPackingCharge": 10.0, "totalTaxes": 18.0, "createdAt": 1717046400, "total": 208.0, "deliveryMode": "delivery"}, "payment": {"mode": "cash", "status": "paid", "amountPaid": 208.0}, "customer": {"firstName": "Platform", "lastName": "Delivery"}, "item": [{"itemId": "ITEM005", "name": "Delivery Item", "quantity": 1, "unitPrice": 180.0, "taxes": [{"title": "Delivery Tax", "value": 18.0, "percentage": 10.0, "liability_on": "aggregator"}], "charges": [{"title": "Delivery Charge", "value": 10.0, "liability_on": "aggregator"}]}]}]}}, "invalid_cases": {"1_missing_required_fields": {"description": "Orders missing required fields", "payloads": [{"description": "Missing orderId", "expected_errors": ["orderId is required and cannot be empty"], "payload": {"orderInfo": {"restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, {"description": "Missing restId", "expected_errors": ["restId is required and cannot be empty"], "payload": {"orderInfo": {"orderId": "ORD001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, {"description": "Missing customer firstName", "expected_errors": ["firstName is required and cannot be empty"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, {"description": "Empty item array", "expected_errors": ["item array is required and must contain at least one item"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": []}}]}}}}