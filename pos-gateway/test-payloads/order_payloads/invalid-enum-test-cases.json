{"invalid_enum_test_cases": {"1_invalid_status": {"description": "Invalid status values", "expected_errors": ["status must be 'placed'"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "confirmed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, "2_invalid_payment_mode": {"description": "Invalid payment mode", "expected_errors": ["payment mode must be one of: online, cash, card, upi"], "payload": {"orderInfo": {"orderId": "ORD002", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "crypto", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, "3_invalid_payment_status": {"description": "Invalid payment status", "expected_errors": ["payment status must be 'paid'"], "payload": {"orderInfo": {"orderId": "ORD003", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "pending", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, "4_invalid_delivery_mode": {"description": "Invalid delivery mode", "expected_errors": ["deliveryMode must be one of: self_delivery, delivery"], "payload": {"orderInfo": {"orderId": "ORD004", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0, "deliveryMode": "pickup"}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}, "5_invalid_liability_on": {"description": "Invalid liability_on values in taxes and charges", "expected_errors": ["liability_on must be one of: restaurant, aggregator"], "payload": {"orderInfo": {"orderId": "ORD005", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "customer"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "vendor"}]}]}}, "6_negative_values": {"description": "Negative values in various fields", "expected_errors": ["quantity is required and must be greater than 0", "unitPrice is required and cannot be negative", "tax value is required and cannot be negative", "charge value is required and cannot be negative"], "payload": {"orderInfo": {"orderId": "ORD006", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": -1, "unitPrice": -100.0, "taxes": [{"title": "GST", "value": -10.0, "percentage": -5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": -5.0, "liability_on": "aggregator"}]}]}}, "7_invalid_created_at": {"description": "Invalid createdAt timestamp", "expected_errors": ["createdAt is required and must be a valid Unix timestamp"], "payload": {"orderInfo": {"orderId": "ORD007", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": -1, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 10.0, "percentage": 5.0, "liability_on": "restaurant"}], "charges": [{"title": "Service Charge", "value": 5.0, "liability_on": "aggregator"}]}]}}}}