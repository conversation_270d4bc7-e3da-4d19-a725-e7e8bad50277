{"description": "Comprehensive test cases for the unified order structure", "version": "1.0", "test_cases": {"valid_cases": {"1_basic_order": {"description": "Basic order with single item, no variants or addons", "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "instruction": "Please prepare fresh", "status": "placed", "subTotal": 250.0, "totalPackingCharge": 10.0, "totalTaxes": 22.5, "createdAt": 1717046400, "total": 282.5, "deliveryMode": "delivery"}, "payment": {"mode": "online", "status": "paid", "amountPaid": 282.5}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "item": [{"itemId": "ITEM001", "name": "Chicken Biryani", "quantity": 1, "unitPrice": 250.0, "taxes": [{"title": "SGST", "value": 11.25, "percentage": 4.5, "liability_on": "restaurant"}, {"title": "CGST", "value": 11.25, "percentage": 4.5, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 10.0, "liability_on": "restaurant"}]}]}}, "2_order_with_variants": {"description": "Order with single item having variants", "payload": {"orderInfo": {"orderId": "ORD002", "restId": "REST1001", "status": "placed", "subTotal": 350.0, "totalPackingCharge": 15.0, "totalTaxes": 31.5, "createdAt": 1717046500, "total": 396.5, "deliveryMode": "self_delivery"}, "payment": {"mode": "cash", "status": "paid", "amountPaid": 396.5}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "item": [{"itemId": "ITEM002", "name": "Pizza Margherita", "quantity": 1, "unitPrice": 300.0, "instruction": "Extra cheese", "taxes": [{"title": "GST", "value": 27.0, "percentage": 9.0, "liability_on": "aggregator"}], "charges": [{"title": "Service Charge", "value": 15.0, "liability_on": "restaurant"}], "variants": [{"id": "VAR001", "name": "Large Size", "unitPrice": 50.0}]}]}}, "3_order_with_addons": {"description": "Order with single item having addons", "payload": {"orderInfo": {"orderId": "ORD003", "restId": "REST1002", "instruction": "Make it spicy", "status": "placed", "subTotal": 320.0, "totalPackingCharge": 12.0, "totalTaxes": 28.8, "createdAt": 1717046600, "total": 360.8}, "payment": {"mode": "card", "status": "paid", "amountPaid": 360.8}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "item": [{"itemId": "ITEM003", "name": "Burger Deluxe", "quantity": 1, "unitPrice": 280.0, "taxes": [{"title": "VAT", "value": 25.2, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Packaging", "value": 12.0, "liability_on": "aggregator"}], "addOns": [{"id": "ADDON001", "name": "Extra Cheese", "unitPrice": 25.0}, {"id": "ADDON002", "name": "<PERSON>", "unitPrice": 15.0}]}]}}, "4_order_with_variants_and_addons": {"description": "Order with item having both variants and addons", "payload": {"orderInfo": {"orderId": "ORD004", "restId": "REST1003", "instruction": "Extra spicy with less oil", "status": "placed", "subTotal": 450.0, "totalPackingCharge": 20.0, "totalTaxes": 40.5, "createdAt": 1717046700, "total": 510.5, "deliveryMode": "delivery"}, "payment": {"mode": "upi", "status": "paid", "amountPaid": 510.5}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "item": [{"itemId": "ITEM004", "name": "Chicken Tikka Masala", "quantity": 2, "unitPrice": 200.0, "instruction": "Medium spice level", "taxes": [{"title": "SGST", "value": 18.0, "percentage": 4.5, "liability_on": "restaurant"}, {"title": "CGST", "value": 18.0, "percentage": 4.5, "liability_on": "restaurant"}], "charges": [{"title": "Delivery Charge", "value": 15.0, "liability_on": "aggregator"}, {"title": "Packing Charge", "value": 5.0, "liability_on": "restaurant"}], "variants": [{"id": "VAR002", "name": "Family Size", "unitPrice": 25.0, "addOns": [{"id": "ADDON003", "name": "Extra Naan", "unitPrice": 30.0}]}], "addOns": [{"id": "ADDON004", "name": "Basmati Rice", "unitPrice": 40.0}, {"id": "ADDON005", "name": "Mint Chutney", "unitPrice": 10.0}]}]}}, "5_order_with_nested_variants": {"description": "Order with nested variants (variant within variant)", "payload": {"orderInfo": {"orderId": "ORD005", "restId": "REST1004", "status": "placed", "subTotal": 380.0, "totalPackingCharge": 18.0, "totalTaxes": 34.2, "createdAt": 1717046800, "total": 432.2, "deliveryMode": "self_delivery"}, "payment": {"mode": "online", "status": "paid", "amountPaid": 432.2}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "item": [{"itemId": "ITEM005", "name": "Custom Sandwich", "quantity": 1, "unitPrice": 300.0, "taxes": [{"title": "Service Tax", "value": 27.0, "percentage": 9.0, "liability_on": "aggregator"}], "charges": [{"title": "Customization Charge", "value": 18.0, "liability_on": "restaurant"}], "variants": [{"id": "VAR003", "name": "Bread Type", "unitPrice": 0.0, "variants": [{"id": "VAR004", "name": "Whole Wheat", "unitPrice": 20.0}, {"id": "VAR005", "name": "Multigrain", "unitPrice": 30.0}]}, {"id": "VAR006", "name": "Size", "unitPrice": 50.0}]}]}}, "6_multiple_items_order": {"description": "Order with multiple items having different combinations", "payload": {"orderInfo": {"orderId": "ORD006", "restId": "REST1005", "instruction": "Please deliver by 7 PM", "status": "placed", "subTotal": 750.0, "totalPackingCharge": 25.0, "totalTaxes": 67.5, "createdAt": 1717046900, "total": 842.5, "deliveryMode": "delivery"}, "payment": {"mode": "card", "status": "paid", "amountPaid": 842.5}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "item": [{"itemId": "ITEM006", "name": "Margherita Pizza", "quantity": 1, "unitPrice": 300.0, "taxes": [{"title": "GST", "value": 27.0, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 10.0, "liability_on": "restaurant"}], "variants": [{"id": "VAR007", "name": "Medium", "unitPrice": 0.0}]}, {"itemId": "ITEM007", "name": "Chicken Wings", "quantity": 2, "unitPrice": 150.0, "instruction": "Extra spicy", "taxes": [{"title": "SGST", "value": 13.5, "percentage": 4.5, "liability_on": "aggregator"}, {"title": "CGST", "value": 13.5, "percentage": 4.5, "liability_on": "aggregator"}], "charges": [{"title": "Service Charge", "value": 15.0, "liability_on": "restaurant"}], "addOns": [{"id": "ADDON006", "name": "Blue Cheese Dip", "unitPrice": 25.0}]}, {"itemId": "ITEM008", "name": "<PERSON><PERSON><PERSON>", "quantity": 1, "unitPrice": 120.0, "taxes": [{"title": "VAT", "value": 10.8, "percentage": 9.0, "liability_on": "restaurant"}], "charges": []}]}}, "7_order_with_multiple_charge_types": {"description": "Order with multiple items having different types of charges (for testing UrbanPiper charge extraction)", "payload": {"orderInfo": {"orderId": "ORD007", "restId": "REST1007", "instruction": "Test all charge types", "status": "placed", "subTotal": 600.0, "totalPackingCharge": 30.0, "totalTaxes": 54.0, "createdAt": **********, "total": 684.0, "deliveryMode": "delivery"}, "payment": {"mode": "online", "status": "paid", "amountPaid": 684.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM010", "name": "Pizza Supreme", "quantity": 1, "unitPrice": 300.0, "taxes": [{"title": "GST", "value": 27.0, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 15.0, "liability_on": "restaurant"}, {"title": "Service Charge", "value": 20.0, "liability_on": "aggregator"}]}, {"itemId": "ITEM011", "name": "Chicken Wings", "quantity": 2, "unitPrice": 150.0, "taxes": [{"title": "GST", "value": 27.0, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Delivery Charge", "value": 25.0, "liability_on": "aggregator"}, {"title": "Handling Charge", "value": 10.0, "liability_on": "restaurant"}]}]}}, "8_order_with_zero_charges": {"description": "Order with items having no charges", "payload": {"orderInfo": {"orderId": "ORD008", "restId": "REST1008", "status": "placed", "subTotal": 200.0, "totalPackingCharge": 0.0, "totalTaxes": 18.0, "createdAt": **********, "total": 218.0}, "payment": {"mode": "cash", "status": "paid", "amountPaid": 218.0}, "customer": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "item": [{"itemId": "ITEM009", "name": "Green Salad", "quantity": 1, "unitPrice": 200.0, "taxes": [{"title": "GST", "value": 18.0, "percentage": 9.0, "liability_on": "restaurant"}], "charges": []}]}}, "9_comprehensive_packing_charges_only": {"description": "Comprehensive test covering all scenarios with only packing charges - single item, multiple items, variants, addons, nested structures, different liability types, zero charges, and edge cases", "payload": {"orderInfo": {"orderId": "ORD009", "restId": "REST2001", "instruction": "Comprehensive packing charge test - handle with care", "status": "placed", "subTotal": 1250.0, "totalPackingCharge": 85.0, "totalTaxes": 112.5, "createdAt": 1717047100, "total": 1447.5, "deliveryMode": "delivery"}, "payment": {"mode": "upi", "status": "paid", "amountPaid": 1447.5}, "customer": {"firstName": "Comprehensive", "lastName": "TestUser"}, "item": [{"itemId": "ITEM_BASIC", "name": "Basic Item with Standard Packing", "quantity": 1, "unitPrice": 150.0, "instruction": "Standard packaging required", "taxes": [{"title": "SGST", "value": 6.75, "percentage": 4.5, "liability_on": "restaurant"}, {"title": "CGST", "value": 6.75, "percentage": 4.5, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 10.0, "liability_on": "restaurant"}]}, {"itemId": "ITEM_VARIANT", "name": "Pizza with <PERSON><PERSON>", "quantity": 2, "unitPrice": 200.0, "instruction": "Extra large size with special packaging", "taxes": [{"title": "GST", "value": 36.0, "percentage": 9.0, "liability_on": "aggregator"}], "charges": [{"title": "Packing Charge", "value": 15.0, "liability_on": "aggregator"}], "variants": [{"id": "SIZE_XL", "name": "Extra Large", "unitPrice": 50.0}, {"id": "CRUST_THICK", "name": "<PERSON><PERSON><PERSON>", "unitPrice": 25.0}]}, {"itemId": "ITEM_ADDON", "name": "<PERSON> with Multiple Addons", "quantity": 1, "unitPrice": 180.0, "instruction": "Multiple addons require careful packaging", "taxes": [{"title": "VAT", "value": 16.2, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 12.0, "liability_on": "restaurant"}], "addOns": [{"id": "CHEESE_EXTRA", "name": "Extra Cheese", "unitPrice": 30.0}, {"id": "BACON_STRIPS", "name": "Bacon Strips", "unitPrice": 40.0}, {"id": "AVOCADO_SLICE", "name": "Avocado Slices", "unitPrice": 35.0}]}, {"itemId": "ITEM_COMPLEX", "name": "Complex Item with Variants and Addons", "quantity": 1, "unitPrice": 250.0, "instruction": "Complex structure with nested variants and addons", "taxes": [{"title": "Service Tax", "value": 22.5, "percentage": 9.0, "liability_on": "aggregator"}], "charges": [{"title": "Packing Charge", "value": 20.0, "liability_on": "aggregator"}], "variants": [{"id": "MEAL_TYPE", "name": "Meal Type Selection", "unitPrice": 0.0, "variants": [{"id": "COMBO_LARGE", "name": "Large Combo", "unitPrice": 75.0}], "addOns": [{"id": "DRINK_UPGRADE", "name": "Drink Upgrade", "unitPrice": 25.0}]}], "addOns": [{"id": "SIDE_FRIES", "name": "French Fries", "unitPrice": 45.0}, {"id": "DESSERT_ICE", "name": "Ice Cream", "unitPrice": 55.0}]}, {"itemId": "ITEM_BULK", "name": "Bulk Order Item", "quantity": 5, "unitPrice": 80.0, "instruction": "Bulk packaging for 5 items", "taxes": [{"title": "IGST", "value": 36.0, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 8.0, "liability_on": "restaurant"}]}, {"itemId": "ITEM_PREMIUM", "name": "Premium Item with High-End Packaging", "quantity": 1, "unitPrice": 350.0, "instruction": "Premium packaging required for delicate item", "taxes": [{"title": "Luxury Tax", "value": 31.5, "percentage": 9.0, "liability_on": "aggregator"}], "charges": [{"title": "Packing Charge", "value": 25.0, "liability_on": "aggregator"}], "variants": [{"id": "PREMIUM_WRAP", "name": "Premium Gift Wrapping", "unitPrice": 50.0}]}, {"itemId": "ITEM_NO_CHARGE", "name": "Item with Zero Packing Charge", "quantity": 1, "unitPrice": 120.0, "instruction": "No packaging required - digital item", "taxes": [{"title": "Digital Tax", "value": 10.8, "percentage": 9.0, "liability_on": "restaurant"}], "charges": []}, {"itemId": "ITEM_MINIMAL", "name": "Minimal Packaging Item", "quantity": 3, "unitPrice": 45.0, "instruction": "Eco-friendly minimal packaging", "taxes": [{"title": "Eco Tax", "value": 12.15, "percentage": 9.0, "liability_on": "restaurant"}], "charges": [{"title": "Packing Charge", "value": 2.0, "liability_on": "restaurant"}]}]}}}, "invalid_cases": {"1_missing_required_fields": {"description": "Orders missing required fields", "payloads": [{"description": "Missing orderId", "expected_errors": ["orderId is required and cannot be empty"], "payload": {"orderInfo": {"restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [], "charges": []}]}}, {"description": "Missing payment mode", "expected_errors": ["payment mode is required and cannot be empty"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [], "charges": []}]}}, {"description": "Missing customer firstName", "expected_errors": ["firstName is required and cannot be empty"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [], "charges": []}]}}, {"description": "Empty item array", "expected_errors": ["item array is required and must contain at least one item"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": []}}]}, "2_invalid_values": {"description": "Orders with invalid field values", "payloads": [{"description": "Negative quantity", "expected_errors": ["quantity is required and must be greater than 0"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": -1, "unitPrice": 100.0, "taxes": [], "charges": []}]}}, {"description": "Invalid payment status", "expected_errors": ["payment status must be 'paid'"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "pending", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [], "charges": []}]}}, {"description": "Invalid delivery mode", "expected_errors": ["invalid delivery mode, must be one of: delivery, self_delivery"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0, "deliveryMode": "invalid_mode"}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [], "charges": []}]}}, {"description": "Invalid tax liability", "expected_errors": ["liability_on must be one of: restaurant, aggregator"], "payload": {"orderInfo": {"orderId": "ORD001", "restId": "REST1001", "status": "placed", "subTotal": 100.0, "totalPackingCharge": 5.0, "totalTaxes": 10.0, "createdAt": 1717046400, "total": 115.0}, "payment": {"mode": "online", "status": "paid", "amountPaid": 115.0}, "customer": {"firstName": "Test", "lastName": "User"}, "item": [{"itemId": "ITEM001", "name": "Test Item", "quantity": 1, "unitPrice": 100.0, "taxes": [{"title": "GST", "value": 9.0, "percentage": 9.0, "liability_on": "invalid_liability"}], "charges": []}]}}]}}}}