#!/bin/bash

# Wait for MongoDB to be ready
echo "Waiting for MongoDB to be ready..."
until mongosh --host mongodb:27017 --eval "print('MongoDB is ready')" > /dev/null 2>&1; do
  echo "MongoDB is not ready yet, waiting..."
  sleep 2
done

echo "MongoDB is ready, initializing replica set..."

# Initialize replica set
mongosh --host mongodb:27017 --eval "
try {
  rs.status();
  print('Replica set already initialized');
} catch (e) {
  print('Initializing replica set...');
  rs.initiate({
    _id: 'rapidoReplSetv4',
    members: [
      { _id: 0, host: 'mongodb:27017' }
    ]
  });
  
  // Wait for replica set to be ready
  while (rs.status().ok !== 1) {
    print('Waiting for replica set to be ready...');
    sleep(1000);
  }
  
  print('Replica set initialized successfully');
}
"

echo "Replica set setup completed!"
