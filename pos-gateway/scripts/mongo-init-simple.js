// MongoDB initialization script for local development
// This script creates the necessary database and collections for the POS Gateway

// Switch to the restaurants database
db = db.getSiblingDB('restaurants');

// Create collections if they don't exist
db.createCollection('pos_menu');
db.createCollection('pos_order');

// Create indexes for better performance
db.pos_menu.createIndex({ "provider": 1, "restaurantId": 1 });
db.pos_menu.createIndex({ "provider": 1, "createdAt": 1 });

db.pos_order.createIndex({ "provider": 1, "orderId": 1 });
db.pos_order.createIndex({ "provider": 1, "restaurantId": 1 });
db.pos_order.createIndex({ "provider": 1, "created_at": 1 });

print('MongoDB initialization completed successfully');
print('Created collections: pos_menu, pos_order');
print('Created indexes for better performance');
