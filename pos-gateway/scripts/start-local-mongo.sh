#!/bin/bash

# Script to start local MongoDB with Docker Compose for POS Gateway development

echo "Starting local MongoDB for POS Gateway..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker first."
    exit 1
fi

# Navigate to the pos-gateway directory
cd "$(dirname "$0")/.."

# Start MongoDB and Kafka services
echo "Starting MongoDB and Kafka services..."
docker-compose up -d mongodb mongo-setup kafka zookeeper

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Check if MongoDB is ready
echo "Checking MongoDB status..."
docker-compose exec mongodb mongosh --eval "rs.status()" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ MongoDB replica set is ready!"
else
    echo "⚠️  MongoDB might still be initializing. Please wait a moment and check manually."
fi

# Check if Kafka is ready
echo "Checking Kafka status..."
docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Kafka is ready!"
else
    echo "⚠️  Kafka might still be starting up."
fi

echo ""
echo "🚀 Local development environment is ready!"
echo ""
echo "MongoDB Connection String: mongodb://localhost:27017/restaurants?replicaSet=rapidoReplSetv4"
echo "Kafka Bootstrap Servers: localhost:9092"
echo ""
echo "To start the POS Gateway server with local config:"
echo "  make build-run-server local"
echo ""
echo "To stop the services:"
echo "  docker-compose down"
echo ""
echo "To view logs:"
echo "  docker-compose logs -f mongodb"
echo "  docker-compose logs -f kafka"
