package common

// UnifiedMenu represents the unified menu structure
type UnifiedMenu struct {
	Restaurant          *Restaurant          `json:"restaurant,omitempty"`
	Categories          []Category           `json:"categories"`
	Timings             []Timing             `json:"timings"`
	OrderBillComponents *OrderBillComponents `json:"orderBillComponents,omitempty"`
	AddOnGroups         []AddOnGroup         `json:"addonGroups"`
	VariantGroups       []VariantGroup       `json:"variantGroups"`
	Variants            []Variant            `json:"variants"`
	BillComponents      *BillComponents      `json:"billComponents,omitempty"`
	CallbackURL         string               `json:"callbackUrl,omitempty"`
}

// Restaurant represents restaurant details
type Restaurant struct {
	Id                   *string `json:"id,omitempty"`
	MenuSharingCode      *string `json:"menuSharingCode,omitempty"`
	ProviderId           *string `json:"providerId,omitempty"`
	OrderingEnabled      bool    `json:"orderingEnabled"`
	MinDeliveryTime      *string `json:"minDeliveryTime,omitempty"`
	Provider             string  `json:"provider,omitempty"`
	ProviderAbbreviation string  `json:"providerAbbreviation,omitempty"`
	Timings              *string `json:"timings,omitempty" example:"123"`
}

// Category represents a menu category
type Category struct {
	ID            *string    `json:"id,omitempty"`
	ProviderId    *string    `json:"providerId,omitempty"`
	Name          *string    `json:"name,omitempty"`
	Description   *string    `json:"description,omitempty"`
	ImageURL      *string    `json:"imageUrl,omitempty"`
	SortOrder     int        `json:"sortOrder"`
	Items         []Item     `json:"items,omitempty"`
	Subcategories []Category `json:"subcategories,omitempty"`
	Timings       *string    `json:"timings,omitempty"`
}

// Item represents a menu item
type Item struct {
	MenuSharingCode  *string             `json:"menuSharingCode,omitempty"`
	ID               *string             `json:"id,omitempty"`
	ProviderId       *string             `json:"providerId,omitempty"`
	Name             *string             `json:"name,omitempty"`
	Description      *string             `json:"description,omitempty"`
	ImageURL         *string             `json:"imageUrl,omitempty"`
	Price            *float64            `json:"price,omitempty"`
	MarkupPrice      *float64            `json:"markupPrice,omitempty"`
	InStock          *bool               `json:"inStock,omitempty"`
	Recommended      *bool               `json:"recommended,omitempty"`
	IsFavorite       *bool               `json:"isFavorite,omitempty"`
	FoodType         *string             `json:"foodType,omitempty"` // ENUM: "veg", "non veg", "egg"
	Tags             []string            `json:"tags"`
	VariantGroupIDs  []string            `json:"variantGroupIds"`
	AddOnGroupIDs    []string            `json:"addonGroupIds"`
	NutritionalInfo  *NutritionalInfo    `json:"nutritionalInfo,omitempty"`
	BillComponents   *ItemBillComponents `json:"billComponents,omitempty"`
	FulfillmentModes []string            `json:"fulfillmentModes"`
	SortOrder        int                 `json:"sortOrder"`
	CategoryID       *string             `json:"categoryId,omitempty"`
	SubcategoryID    *string             `json:"subcategoryId,omitempty"`
	IgnoreOn         *IgnoreOn           `json:"ignoreOn,omitempty"`
}

// VariantGroup represents a group of variants
type VariantGroup struct {
	ID         *string  `json:"id,omitempty"`
	ProviderId *string  `json:"providerId,omitempty"`
	Name       *string  `json:"name,omitempty"`
	VariantIDs []string `json:"variantIds"`
	SortOrder  int      `json:"sortOrder"`
}

// Variant represents a variant option
type Variant struct {
	ID               *string             `json:"id,omitempty"`
	ProviderId       *string             `json:"providerId,omitempty"`
	Name             *string             `json:"name,omitempty"`
	Price            *float64            `json:"price,omitempty"`
	MarkupPrice      *float64            `json:"markupPrice,omitempty"`
	InStock          *bool               `json:"inStock,omitempty"`
	FoodType         *string             `json:"foodType,omitempty"`
	NutritionalInfo  *NutritionalInfo    `json:"nutritionalInfo,omitempty"`
	FulfillmentModes []string            `json:"fulfillmentModes"`
	VariantGroupIDs  []string            `json:"variantGroupIds"`
	AddOnGroupIDs    []string            `json:"addonGroupIds"`
	BillComponents   *ItemBillComponents `json:"billComponents,omitempty"`
	SortOrder        int                 `json:"sortOrder"`
}

// AddOnGroup represents a group of add-ons
type AddOnGroup struct {
	ID             *string `json:"id,omitempty"`
	ProviderId     *string `json:"providerId,omitempty"`
	Name           *string `json:"name,omitempty"`
	MinimumNeeded  *int    `json:"minimumNeeded,omitempty"`
	MaximumAllowed *int    `json:"maximumAllowed,omitempty"`
	AddOns         []AddOn `json:"addOns"`
	SortOrder      int     `json:"sortOrder"`
}

// AddOn represents an add-on option
type AddOn struct {
	ID               *string          `json:"id,omitempty"`
	ProviderId       *string          `json:"providerId,omitempty"`
	Name             *string          `json:"name,omitempty"`
	Price            *float64         `json:"price,omitempty"`
	InStock          *bool            `json:"inStock,omitempty"`
	FoodType         *string          `json:"foodType,omitempty"`
	NutritionalInfo  *NutritionalInfo `json:"nutritionalInfo,omitempty"`
	FulfillmentModes []string         `json:"fulfillmentModes"`
	SortOrder        int              `json:"sortOrder" example:"0"`
	ImageURL         string           `json:"imageUrl"`
}

// NutritionalInfo represents nutritional information
type NutritionalInfo struct {
	Carbohydrate   *NutritionalValue           `json:"carbohydrate,omitempty"`
	Fiber          *NutritionalValue           `json:"fiber,omitempty"`
	Protein        *NutritionalValue           `json:"protein,omitempty"`
	Calorie        *NutritionalValue           `json:"calorie,omitempty"`
	Sodium         *NutritionalValue           `json:"sodium,omitempty"`
	Cholesterol    *NutritionalValue           `json:"cholesterol,omitempty"`
	FoodAmount     *NutritionalValue           `json:"foodAmount,omitempty"`
	Minerals       *NutritionalValue           `json:"minerals,omitempty"`
	TotalSugar     *NutritionalValue           `json:"totalSugar,omitempty"`
	AddedSugar     *NutritionalValue           `json:"addedSugar,omitempty"`
	TotalFat       *NutritionalValue           `json:"totalFat,omitempty"`
	SaturatedFat   *NutritionalValue           `json:"saturatedFat,omitempty"`
	TransFat       *NutritionalValue           `json:"transFat,omitempty"`
	Vitamins       *NutritionalValue           `json:"vitamins,omitempty"`
	ServingInfo    *string                     `json:"servingInfo,omitempty"`
	AdditiveMap    map[string]NutritionalValue `json:"additiveMap,omitempty"`
	Allergens      *AllergenInfo               `json:"allergens,omitempty"`
	AdditionalInfo *AdditionalInfo             `json:"additionalInfo,omitempty"`
	Salt           *NutritionalValue           `json:"salt,omitempty"`
}

// NutritionalValue represents a nutritional value with unit
type NutritionalValue struct {
	Value *float64 `json:"value,omitempty"`
	Unit  *string  `json:"unit,omitempty"`
	Name  *string  `json:"name,omitempty"`
}

// AllergenInfo represents allergen information
type AllergenInfo struct {
	Allergen     *string `json:"allergen,omitempty"`
	AllergenDesc *string `json:"allergenDesc,omitempty"`
}

// AdditionalInfo represents additional information
type AdditionalInfo struct {
	Info   *string `json:"info,omitempty"`
	Remark *string `json:"remark,omitempty"`
}

// Timing represents operating hours
type Timing struct {
	ID   *string `json:"id,omitempty"`
	Days []Day   `json:"days"`
}

// Day represents a day's operating hours
type Day struct {
	Day   *string `json:"day,omitempty"`
	Slots []Slot  `json:"slots"`
}

// Slot represents a time slot
type Slot struct {
	StartTime *string `json:"startTime,omitempty"`
	EndTime   *string `json:"endTime,omitempty"`
}

// Tax represents a tax
type Tax struct {
	ID               *string  `json:"id,omitempty"`
	ProviderId       *string  `json:"providerId,omitempty"`
	Name             *string  `json:"name,omitempty"`
	Description      *string  `json:"description,omitempty"`
	Value            *float64 `json:"value,omitempty"`
	FulfillmentModes []string `json:"fulfillmentModes"`
}

// BillComponents represents bill components
type BillComponents struct {
	Charges []Charge `json:"charges"`
	Taxes   []Tax    `json:"taxes"`
}

type ItemBillComponents struct {
	Charges []string `json:"charges"`
	TaxIDs  []string `json:"taxIds"`
}

// Charge represents a bill charge
type Charge struct {
	ID               *string  `json:"id,omitempty"`
	ProviderId       *string  `json:"providerId,omitempty"`
	Name             *string  `json:"name,omitempty"`
	Description      *string  `json:"description,omitempty"`
	Value            *float64 `json:"value,omitempty"`
	Type             *string  `json:"type,omitempty"`
	FulfillmentModes []string `json:"fulfillmentModes"`
	Taxes            []string `json:"taxes"`
}

// OrderBillComponents represents order-level bill components
type OrderBillComponents struct {
	Charges []Charge `json:"charges"`
}

// IgnoreOn represents what to ignore for an item
type IgnoreOn struct {
	Tax       bool `json:"tax"`
	Discounts bool `json:"discounts"`
}
