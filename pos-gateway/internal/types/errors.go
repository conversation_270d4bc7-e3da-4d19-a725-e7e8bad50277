package types

type StatusError struct {
	DisplayMessage string                  `json:"displayMessage,omitempty"`
	Message        string                  `json:"message"`
	Code           string                  `json:"code"`
	Data           *map[string]interface{} `json:"data,omitempty"`
	HTTPStatus     int                     `json:"httpStatus,omitempty"`
}

func (e *StatusError) Error() string {
	return e.Message
}

func NewValidationError(message string, data *map[string]interface{}) *StatusError {
	return &StatusError{
		DisplayMessage: "Invalid request",
		Message:        message,
		Code:           "error_processing_request",
		Data:           data,
		HTTPStatus:     400, // Bad Request
	}
}

func NewInternalServerError(message string, data *map[string]interface{}) *StatusError {
	return &StatusError{
		DisplayMessage: "Failed to process request",
		Message:        message,
		Code:           "internal_server_error",
		Data:           data,
		HTTPStatus:     500, // Internal Server Error
	}
}
