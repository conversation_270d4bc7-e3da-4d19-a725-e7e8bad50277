package petpooja

// <PERSON>u represents the Petpooja menu response
type Menu struct {
	Success          string           `json:"success"`
	Message          string           `json:"message"`
	Restaurants      []Restaurant     `json:"restaurants"`
	OrderTypes       []OrderType      `json:"ordertypes"`
	Categories       []Category       `json:"categories"`
	ParentCategories []ParentCategory `json:"parentcategories"`
	Items            []Item           `json:"items"`
	AddonGroups      []AddonGroup     `json:"addongroups"`
	Attributes       []Attribute      `json:"attributes"`
	Taxes            []Tax            `json:"taxes"`
	Timings          []Timing         `json:"timings"`
}

// Restaurant represents a restaurant
type Restaurant struct {
	RestaurantID string            `json:"restaurantid"`
	Active       string            `json:"active"`
	Details      RestaurantDetails `json:"details"`
}

// RestaurantDetails represents restaurant details
type RestaurantDetails struct {
	MenuSharingCode        string   `json:"menusharingcode"`
	CurrencyHTML           string   `json:"currency_html"`
	Country                string   `json:"country"`
	CountryID              string   `json:"country_id"`
	InvoiceOOA             int      `json:"invoice_ooa"`
	Images                 []string `json:"images"`
	RestaurantName         string   `json:"restaurantname"`
	Address                string   `json:"address"`
	Contact                string   `json:"contact"`
	Latitude               string   `json:"latitude"`
	Longitude              string   `json:"longitude"`
	Landmark               string   `json:"landmark"`
	City                   string   `json:"city"`
	CityID                 string   `json:"city_id"`
	State                  string   `json:"state"`
	StateID                string   `json:"state_id"`
	MinimumOrderAmount     string   `json:"minimumorderamount"`
	MinimumDeliveryTime    string   `json:"minimumdeliverytime"`
	MinimumPrepTime        string   `json:"minimum_prep_time"`
	DeliveryCharge         string   `json:"deliverycharge"`
	DeliveryHoursFrom1     string   `json:"deliveryhoursfrom1"`
	DeliveryHoursTo1       string   `json:"deliveryhoursto1"`
	DeliveryHoursFrom2     string   `json:"deliveryhoursfrom2"`
	DeliveryHoursTo2       string   `json:"deliveryhoursto2"`
	CalculateTaxOnPacking  int      `json:"calculatetaxonpacking"`
	CalculateTaxOnDelivery int      `json:"calculatetaxondelivery"`
	DCTaxesID              string   `json:"dc_taxes_id"`
	PCTaxesID              string   `json:"pc_taxes_id"`
	PackagingApplicableOn  string   `json:"packaging_applicable_on"`
	PackagingCharge        string   `json:"packaging_charge"`
	PackagingChargeType    string   `json:"packaging_charge_type"`
}

// OrderType represents an order type
type OrderType struct {
	OrderTypeID int    `json:"ordertypeid"`
	OrderType   string `json:"ordertype"`
}

// Category represents a menu category
type Category struct {
	CategoryID       string     `json:"categoryid"`
	Active           string     `json:"active"`
	CategoryRank     string     `json:"categoryrank"`
	ParentCategoryID string     `json:"parent_category_id"`
	Tags             []string   `json:"tags"`
	CategoryName     string     `json:"categoryname"`
	CategoryTimings  string     `json:"categorytimings"`
	CategoryImageURL string     `json:"category_image_url"`
	Description      string     `json:"description"`
	Subcategories    []Category `json:"subcategories"`
}

// ParentCategory represents a parent category
type ParentCategory struct {
	Name     string `json:"name"`
	Rank     string `json:"rank"`
	ImageURL string `json:"image_url"`
	Status   string `json:"status"`
	ID       string `json:"id"`
}

// Item represents a menu item
type Item struct {
	ItemID             string           `json:"itemid"`
	ItemRank           string           `json:"itemrank"`
	ItemCategoryID     string           `json:"item_categoryid"`
	ItemOrderType      string           `json:"item_ordertype"`
	ItemPackingCharges string           `json:"item_packingcharges"`
	ItemAllowAddon     string           `json:"itemallowaddon"`
	ItemAddonBasedOn   string           `json:"itemaddonbasedon"`
	ItemFavorite       string           `json:"item_favorite"`
	IgnoreTaxes        string           `json:"ignore_taxes"`
	IgnoreDiscounts    string           `json:"ignore_discounts"`
	InStock            string           `json:"in_stock"`
	Cuisine            []string         `json:"cuisine"`
	VariationGroupName string           `json:"variation_groupname"`
	Variation          []ItemVariation  `json:"variation"`
	Addon              []ItemAddon      `json:"addon"`
	IsRecommend        string           `json:"is_recommend"`
	ItemName           string           `json:"itemname"`
	ItemAttributeID    string           `json:"item_attributeid"`
	ItemAttribute      string           `json:"item_attribute"`
	ItemDescription    string           `json:"itemdescription"`
	MinPrepTime        string           `json:"minimumpreparationtime"`
	Price              string           `json:"price"`
	Active             string           `json:"active"`
	MarkupPrice        string           `json:"markup_price"`
	ItemTags           []string         `json:"item_tags"`
	ItemInfo           ItemInfo         `json:"item_info"`
	ItemImageURL       string           `json:"item_image_url"`
	ItemTax            string           `json:"item_tax"`
	TaxInclusive       bool             `json:"tax_inclusive"`
	GSTType            string           `json:"gst_type"`
	NutritionalInfo    *NutritionalInfo `json:"nutrition,omitempty"`
	FulfillmentModes   []string         `json:"fulfillment_modes,omitempty"`
}

// ItemInfo represents item additional information
type ItemInfo struct {
	SpiceLevel string `json:"spice_level"`
}

// ItemVariation represents an item variation
type ItemVariation struct {
	ID                  string      `json:"id"`
	VariationID         string      `json:"variationid"`
	Name                string      `json:"name"`
	GroupName           string      `json:"groupname"`
	Price               string      `json:"price"`
	MarkupPrice         string      `json:"markup_price"`
	Active              string      `json:"active"`
	ItemPackingCharges  string      `json:"item_packingcharges"`
	VariationRank       string      `json:"variationrank"`
	Addon               []ItemAddon `json:"addon"`
	VariationAllowAddon int         `json:"variationallowaddon"`
	FulfillmentModes    []string    `json:"fulfillment_modes,omitempty"`
}

// ItemAddon represents an item add-on reference
type ItemAddon struct {
	AddonGroupID          string `json:"addon_group_id"`
	AddonItemSelectionMin string `json:"addon_item_selection_min"`
	AddonItemSelectionMax string `json:"addon_item_selection_max"`
}

// Variation represents a variation option
type Variation struct {
	VariationID string `json:"variationid"`
	Name        string `json:"name"`
	GroupName   string `json:"groupname"`
	Status      string `json:"status"`
}

// AddonGroup represents an add-on group
type AddonGroup struct {
	AddonGroupID    string      `json:"addongroupid"`
	AddonGroupRank  string      `json:"addongroup_rank"`
	Active          string      `json:"active"`
	AddonGroupItems []AddonItem `json:"addongroupitems"`
	AddonGroupName  string      `json:"addongroup_name"`
}

// AddonItem represents an add-on item
type AddonItem struct {
	AddonItemID      string   `json:"addonitemid"`
	AddonItemName    string   `json:"addonitem_name"`
	AddonItemPrice   string   `json:"addonitem_price"`
	Active           string   `json:"active"`
	Attributes       string   `json:"attributes"`
	ItemAttributeID  string   `json:"item_attributeid"`
	ItemAttribute    string   `json:"item_attribute"`
	AddonItemRank    string   `json:"addonitem_rank"`
	FulfillmentModes []string `json:"fulfillment_modes,omitempty"`
}

// Attribute represents a food attribute (veg/non-veg)
type Attribute struct {
	AttributeID string `json:"attributeid"`
	Attribute   string `json:"attribute"`
	Active      string `json:"active"`
}

// Tax represents a tax configuration
type Tax struct {
	TaxID                string `json:"taxid"`
	TaxName              string `json:"taxname"`
	Tax                  string `json:"tax"`
	TaxType              string `json:"taxtype"`
	TaxOrderType         string `json:"tax_ordertype"`
	Active               string `json:"active"`
	TaxCoreOrTotal       string `json:"tax_coreortotal"`
	TaxTaxType           string `json:"tax_taxtype"`
	Rank                 string `json:"rank"`
	ConsiderInCoreAmount string `json:"consider_in_core_amount"`
	Description          string `json:"description"`
}

// NutritionalInfo represents nutritional information
type NutritionalInfo struct {
	FoodAmount     *NutritionalValue           `json:"foodAmount"`
	Calories       *NutritionalValue           `json:"calories"`
	Protein        *NutritionalValue           `json:"protein"`
	Minerals       *NutritionalValue           `json:"minerals"`
	Sodium         *NutritionalValue           `json:"sodium"`
	Carbohydrate   *NutritionalValue           `json:"carbohydrate"`
	TotalSugar     *NutritionalValue           `json:"totalSugar"`
	AddedSugar     *NutritionalValue           `json:"addedSugar"`
	TotalFat       *NutritionalValue           `json:"totalFat"`
	SaturatedFat   *NutritionalValue           `json:"saturatedFat"`
	TransFat       *NutritionalValue           `json:"transFat"`
	Cholesterol    *NutritionalValue           `json:"cholesterol"`
	Vitamins       *NutritionalValue           `json:"vitamins"`
	Fiber          *NutritionalValue           `json:"fiber"`
	ServingInfo    *string                     `json:"servingInfo"`
	AdditiveMap    map[string]NutritionalValue `json:"additiveMap"`
	Allergens      *AllergenInfo               `json:"allergens"`
	AdditionalInfo *AdditionalInfo             `json:"additionalInfo"`
}

// NutritionalValue represents a nutritional value with unit
type NutritionalValue struct {
	Amount *float64 `json:"amount"`
	Unit   *string  `json:"unit"`
	Name   *string  `json:"name,omitempty"`
}

// AllergenInfo represents allergen information
type AllergenInfo struct {
	Allergen     *string `json:"allergen"`
	AllergenDesc *string `json:"allergenDesc"`
}

// AdditionalInfo represents additional information
type AdditionalInfo struct {
	Info   *string `json:"info"`
	Remark *string `json:"remark"`
}

// Timing represents restaurant timing information
type Timing struct {
	ID   string `json:"id"`
	Days []Day  `json:"days"`
}

// Day represents timing information for a specific day
type Day struct {
	Day   string `json:"day"`
	Slots []Slot `json:"slots"`
}

// Slot represents a time slot
type Slot struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}
