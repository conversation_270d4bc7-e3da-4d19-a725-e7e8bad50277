package transformers

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
)

type Transformers interface {
	TransformProviderMenuIntoUnifiedMenu(menu *map[string]interface{}, clientId string) *common.UnifiedMenu
	TransformProviderInventoryIntoUnifiedInventory(inventory *map[string]interface{}, clientId string) *common.InventoryUpdate
	TransformProviderStoreIntoUnifiedStoreStatus(store *map[string]interface{}, clientId string) *common.StoreStatus
	TransformUnifiedOrderToProviderOrder(order *orders.Order, clientId string) interface{}
	TransformUnifiedOrderToPetpoojaOrder(order *orders.Order) interface{}
	TransformUnifiedOrderToUrbanPiperOrder(order *orders.Order) interface{}
}

type transformersImpl struct {
	utils utils.Utils
}

func NewTransformers(utils utils.Utils) Transformers {
	return &transformersImpl{
		utils: utils,
	}
}

func (t *transformersImpl) TransformProviderMenuIntoUnifiedMenu(menu *map[string]interface{}, clientId string) *common.UnifiedMenu {
	switch clientId {
	case constants.PetpoojaClientId:
		return t.TransformPetpoojaMenu(menu)
	case constants.UrbanpiperClientId:
		return t.TransformUrbanPiperMenu(menu)
	}
	return t.TransformIntoUnifiedMenu(menu, clientId)
}

func (t *transformersImpl) TransformProviderInventoryIntoUnifiedInventory(inventory *map[string]interface{}, clientId string) *common.InventoryUpdate {
	switch clientId {
	case constants.PetpoojaClientId:
		return t.TransformPetpoojaInventory(inventory)
	case constants.UrbanpiperClientId:
		return t.TransformUrbanPiperInventory(inventory)
	}
	var unifiedInventory common.InventoryUpdate
	err := utils.UnmarshalJSONToInterface(inventory, &unifiedInventory)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling unifiedInventory inventory",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}
	return &unifiedInventory
}

func (t *transformersImpl) TransformProviderStoreIntoUnifiedStoreStatus(storeStatus *map[string]interface{}, clientId string) *common.StoreStatus {
	switch clientId {
	case constants.PetpoojaClientId:
		return t.TransformPetpoojaProviderStoreIntoUnifiedStoreStatus(storeStatus)
	case constants.UrbanpiperClientId:
		return t.TransformUrbanStoreStatus(storeStatus)
	}
	var unifiedMenu common.StoreStatus
	err := utils.UnmarshalJSONToInterface(storeStatus, &unifiedMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling unifiedMenu menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}
	return &unifiedMenu
}

func (t *transformersImpl) TransformUnifiedOrderToProviderOrder(order *orders.Order, clientId string) interface{} {
	switch clientId {
	case constants.PetpoojaClientId:
		return t.TransformUnifiedOrderToPetpoojaOrder(order)
	case constants.UrbanpiperClientId:
		return t.TransformUnifiedOrderToUrbanPiperOrder(order)
	}

	// Default case - return the order as-is (no map conversion)
	// For unknown providers, return the unified order structure directly
	return order
}
