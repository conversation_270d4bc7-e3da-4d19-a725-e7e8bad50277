package transformers

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	petpoojaTypes "github.com/nutanalabs/pos-gateway/internal/posclients/petpooja/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/types/petpooja"
)

// convertMinutesToHHMM converts minutes string (e.g., "30 Minutes") to HH:MM format
func convertMinutesToHHMM(minutesStr string) string {
	// Extract the number from the string
	minutesStr = strings.TrimSpace(strings.ReplaceAll(minutesStr, "Minutes", ""))
	minutes, err := strconv.Atoi(minutesStr)
	if err != nil {
		return ""
	}

	// Convert minutes to hours and remaining minutes
	hours := minutes / 60
	remainingMinutes := minutes % 60

	// Format as HH:MM
	return fmt.Sprintf("%02d:%02d", hours, remainingMinutes)
}

func (t *transformersImpl) TransformPetpoojaMenu(menu *map[string]interface{}) *common.UnifiedMenu {
	var petpoojaMenu petpooja.Menu
	err := utils.UnmarshalJSONToInterface(&menu, &petpoojaMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling Petpooja menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}
	if len(petpoojaMenu.Restaurants) == 0 {
		return nil
	}

	restaurant := t.transformRestaurant(petpoojaMenu.Restaurants[0])
	unifiedMenu := t.transformMenu(petpoojaMenu, *restaurant)

	// Add order bill components from restaurant
	orderBillComponents := t.transformOrderBillComponents(petpoojaMenu.Restaurants[0])
	if orderBillComponents != nil {
		unifiedMenu.OrderBillComponents = orderBillComponents
	}

	// Convert minimum delivery time to HH:MM format
	minDeliveryTime := convertMinutesToHHMM(restaurant.Details.MinimumDeliveryTime)

	// Add restaurant details
	unifiedMenu.Restaurant = &common.Restaurant{
		ProviderId:           &restaurant.RestaurantID,
		OrderingEnabled:      restaurant.Active == "1",
		MinDeliveryTime:      &minDeliveryTime,
		Provider:             constants.PetpoojaProvider,
		ProviderAbbreviation: constants.ProviderAbbreviationMap[constants.PetpoojaProvider],
		MenuSharingCode:      &restaurant.Details.MenuSharingCode,
	}

	// Transform addon groups to root level
	unifiedMenu.AddOnGroups = transformAddonGroupsToRoot(petpoojaMenu.AddonGroups, petpoojaMenu.Attributes, petpoojaMenu.Items)

	// Transform variant groups and variants to root level
	unifiedMenu.VariantGroups, unifiedMenu.Variants = transformVariantGroupsAndVariantsToRoot(petpoojaMenu.Items, *restaurant)

	return &unifiedMenu
}

func (t *transformersImpl) transformOrderBillComponents(restaurant petpooja.Restaurant) *common.OrderBillComponents {
	charges := make([]common.Charge, 0)

	// Handle delivery charge
	if restaurant.Details.DeliveryCharge != "" {
		deliveryCharge, _ := strconv.ParseFloat(restaurant.Details.DeliveryCharge, 64)
		// Skip if charge value is zero
		if deliveryCharge > 0 {
			chargeName := "Delivery Charge"
			chargeType := "FIXED"

			// Use the charge value as the basis for the ID
			chargeID := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + "delivery_" + restaurant.Details.DeliveryCharge
			providerId := restaurant.Details.DeliveryCharge

			// If tax is applicable on delivery charge
			var taxes []string
			if restaurant.Details.CalculateTaxOnDelivery == 1 && restaurant.Details.DCTaxesID != "" {
				taxProviderId := restaurant.Details.DCTaxesID
				taxId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + taxProviderId
				taxes = append(taxes, taxId)
			}

			charges = append(charges, common.Charge{
				ID:         &chargeID,
				ProviderId: &providerId,
				Name:       &chargeName,
				Value:      &deliveryCharge,
				Type:       &chargeType,
				Taxes:      taxes,
			})
		}
	}

	// Handle packaging charge if applicable on ORDER level
	if restaurant.Details.PackagingApplicableOn == "ORDER" && restaurant.Details.PackagingCharge != "" {
		packagingCharge, _ := strconv.ParseFloat(restaurant.Details.PackagingCharge, 64)
		// Skip if charge value is zero
		if packagingCharge > 0 {
			chargeName := "Packaging Charge"
			chargeType := restaurant.Details.PackagingChargeType

			// Use the charge value as the basis for the ID
			chargeID := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + "packaging_" + restaurant.Details.PackagingCharge
			providerId := restaurant.Details.PackagingCharge

			// If tax is applicable on packaging charge
			var taxes []string
			if restaurant.Details.CalculateTaxOnPacking == 1 && restaurant.Details.PCTaxesID != "" {
				taxIDs := strings.Split(restaurant.Details.PCTaxesID, ",")
				for _, taxID := range taxIDs {
					providerId := taxID
					id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId
					taxes = append(taxes, id)
				}
			}

			charges = append(charges, common.Charge{
				ID:         &chargeID,
				ProviderId: &providerId,
				Name:       &chargeName,
				Value:      &packagingCharge,
				Type:       &chargeType,
				Taxes:      taxes,
			})
		}
	}

	if len(charges) > 0 {
		return &common.OrderBillComponents{
			Charges: charges,
		}
	}

	return nil
}

func (t *transformersImpl) transformMenu(menu petpooja.Menu, restaurant petpooja.Restaurant) common.UnifiedMenu {
	// Create a map of parent categories to their child category IDs
	parentCategoryMap := make(map[string][]string)
	for _, cat := range menu.Categories {
		if cat.ParentCategoryID != "" && cat.ParentCategoryID != "0" {
			parentCategoryMap[cat.ParentCategoryID] = append(parentCategoryMap[cat.ParentCategoryID], cat.CategoryID)
		}
	}

	// Create a map of child categories to their parent category IDs for reverse lookup
	childToParentMap := make(map[string]string)
	for _, cat := range menu.Categories {
		if cat.ParentCategoryID != "" && cat.ParentCategoryID != "0" {
			childToParentMap[cat.CategoryID] = cat.ParentCategoryID
		}
	}

	// Transform categories
	categories := make([]common.Category, 0)
	allTimings := make([]common.Timing, 0)
	timingIDMap := make(map[string]bool) // To track unique timing IDs

	// First process parent categories from ParentCategory array
	for _, parentCat := range menu.ParentCategories {
		category := transformParentCategory(parentCat)

		// Get child categories for this parent
		if childCatIDs, exists := parentCategoryMap[parentCat.ID]; exists {
			childCategories := make([]common.Category, 0)
			for _, childID := range childCatIDs {
				// Find and transform child category
				for _, cat := range menu.Categories {
					if cat.CategoryID == childID {
						childCat := transformCategory(cat)
						// Collect timings if present
						if childCat.Timings != nil {
							timings, _ := transformPetPoojaTimings(cat.CategoryTimings)
							if timings != nil {
								for _, timing := range timings {
									if timing.ID != nil && !timingIDMap[*timing.ID] {
										allTimings = append(allTimings, timing)
										timingIDMap[*timing.ID] = true
									}
								}
							}
						}

						childCategories = append(childCategories, childCat)
						break
					}
				}
			}
			category.Subcategories = childCategories
		}

		categories = append(categories, category)
	}

	// Add categories that have no parent (parentCategoryId is "" or "0")
	for _, cat := range menu.Categories {
		if cat.ParentCategoryID == "" || cat.ParentCategoryID == "0" {
			category := transformCategory(cat)
			// Collect timings if present
			if category.Timings != nil {
				timings, _ := transformPetPoojaTimings(cat.CategoryTimings)
				if timings != nil {
					for _, timing := range timings {
						if timing.ID != nil && !timingIDMap[*timing.ID] {
							allTimings = append(allTimings, timing)
							timingIDMap[*timing.ID] = true
						}
					}
				}
			}
			categories = append(categories, category)
		}
	}

	// Transform taxes
	taxes := make([]common.Tax, 0)
	for _, tax := range menu.Taxes {
		taxes = append(taxes, transformTax(tax))
	}

	// Transform charges
	charges := make([]common.Charge, 0)

	// Add packaging charge if applicable at restaurant level
	if restaurant.Details.PackagingApplicableOn == "ORDER" && restaurant.Details.PackagingCharge != "" {
		packagingCharge, _ := strconv.ParseFloat(restaurant.Details.PackagingCharge, 64)
		// Skip if charge value is zero
		if packagingCharge > 0 {
			chargeName := "Packaging Charge"
			chargeType := restaurant.Details.PackagingChargeType

			// Use the charge value as the basis for the ID
			chargeID := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + "packaging_" + restaurant.Details.PackagingCharge
			providerId := restaurant.Details.PackagingCharge

			// If tax is applicable on packaging charge
			var taxIDs []string
			if restaurant.Details.CalculateTaxOnPacking == 1 && restaurant.Details.PCTaxesID != "" {
				taxProviderId := restaurant.Details.PCTaxesID
				taxId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + taxProviderId
				taxIDs = append(taxIDs, taxId)
			}

			charges = append(charges, common.Charge{
				ID:         &chargeID,
				ProviderId: &providerId,
				Name:       &chargeName,
				Value:      &packagingCharge,
				Type:       &chargeType,
				Taxes:      taxIDs,
			})
		}
	}

	// Add delivery charge if applicable
	if restaurant.Details.DeliveryCharge != "" {
		deliveryCharge, _ := strconv.ParseFloat(restaurant.Details.DeliveryCharge, 64)
		// Skip if charge value is zero
		if deliveryCharge > 0 {
			chargeName := "Delivery Charge"
			chargeType := "FIXED"

			// Use the charge value as the basis for the ID
			chargeID := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + "delivery_" + restaurant.Details.DeliveryCharge
			providerId := restaurant.Details.DeliveryCharge

			// If tax is applicable on delivery charge
			var taxIDs []string
			if restaurant.Details.CalculateTaxOnDelivery == 1 && restaurant.Details.DCTaxesID != "" {
				taxProviderId := restaurant.Details.DCTaxesID
				taxId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + taxProviderId
				taxIDs = append(taxIDs, taxId)
			}

			charges = append(charges, common.Charge{
				ID:         &chargeID,
				ProviderId: &providerId,
				Name:       &chargeName,
				Value:      &deliveryCharge,
				Type:       &chargeType,
				Taxes:      taxIDs,
			})
		}
	}

	// Create addon groups map for lookup
	addonGroups := make(map[string]petpooja.AddonGroup)
	for _, ag := range menu.AddonGroups {
		addonGroups[ag.AddonGroupID] = ag
	}

	// Create a map of all packing charges from items to include in the BillComponents at root level
	packingChargeMap := make(map[string]common.Charge)

	// Also create a map to keep track of item IDs to packing charge IDs
	itemPackingChargeMap := make(map[string]string)

	// Transform items and add them to their respective categories
	for _, item := range menu.Items {
		// Use ItemPackingCharges value as the charge ID if it exists and is not zero
		var packingChargeId string
		if item.ItemPackingCharges != "" {
			packingCharge, _ := strconv.ParseFloat(item.ItemPackingCharges, 64)
			if packingCharge > 0 {
				// Use the charge value as the basis for packing charge ID to keep identical charges consistent
				packingChargeId = constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + "PC" + item.ItemPackingCharges
				itemPackingChargeMap[item.ItemID] = packingChargeId
			}
		}

		transformedItem := transformItem(item, restaurant, addonGroups, menu.Taxes, menu.OrderTypes, menu.Attributes, itemPackingChargeMap)

		// Add packaging charge to the map if it exists
		if item.ItemPackingCharges != "" {
			packingCharge, _ := strconv.ParseFloat(item.ItemPackingCharges, 64)
			// Skip if charge value is zero
			if packingCharge > 0 {
				chargeName := "Packing Charge"
				chargeType := restaurant.Details.PackagingChargeType

				packingChargeMap[packingChargeId] = common.Charge{
					ID:         &packingChargeId,
					ProviderId: &item.ItemPackingCharges,
					Name:       &chargeName,
					Value:      &packingCharge,
					Type:       &chargeType,
				}
			}
		}

		// Check if this item's category is a subcategory
		if parentID, isSubcategory := childToParentMap[item.ItemCategoryID]; isSubcategory {
			// If it's a subcategory, set the category_id to parent and subcategory_id to current
			parentProviderId := parentID
			parentId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + parentProviderId
			transformedItem.CategoryID = &parentId

			subcatProviderId := item.ItemCategoryID
			subcatId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + subcatProviderId
			transformedItem.SubcategoryID = &subcatId
		}

		// Find the category and add the item
		for i := range categories {
			if categories[i].ProviderId != nil && *categories[i].ProviderId == item.ItemCategoryID {
				if categories[i].Items == nil {
					categories[i].Items = make([]common.Item, 0)
				}
				categories[i].Items = append(categories[i].Items, transformedItem)
				break
			}
			// Also check in subcategories
			if categories[i].Subcategories != nil {
				for j := range categories[i].Subcategories {
					if categories[i].Subcategories[j].ProviderId != nil && *categories[i].Subcategories[j].ProviderId == item.ItemCategoryID {
						if categories[i].Subcategories[j].Items == nil {
							categories[i].Subcategories[j].Items = make([]common.Item, 0)
						}
						categories[i].Subcategories[j].Items = append(categories[i].Subcategories[j].Items, transformedItem)
						break
					}
				}
			}
		}
	}

	// Convert packaging charge map to slice for BillComponents
	for _, charge := range packingChargeMap {
		charges = append(charges, charge)
	}

	unifiedMenu := common.UnifiedMenu{
		Categories: categories,
		Timings:    allTimings,
		BillComponents: &common.BillComponents{
			Charges: charges,
			Taxes:   taxes,
		},
	}

	return unifiedMenu
}

func transformParentCategory(parentCat petpooja.ParentCategory) common.Category {
	sortOrder := 0
	if parentCat.Rank != "" {
		sortOrder, _ = strconv.Atoi(parentCat.Rank)
	}

	providerId := parentCat.ID
	id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

	return common.Category{
		ID:            &id,
		ProviderId:    &providerId,
		Name:          &parentCat.Name,
		ImageURL:      &parentCat.ImageURL,
		SortOrder:     sortOrder,
		Subcategories: make([]common.Category, 0),
		Items:         make([]common.Item, 0),
	}
}

func transformCategory(cat petpooja.Category) common.Category {
	sortOrder := 0
	if cat.CategoryRank != "" {
		sortOrder, _ = strconv.Atoi(cat.CategoryRank)
	}

	providerId := cat.CategoryID
	id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

	// Transform timings and get timing ID
	_, timingID := transformPetPoojaTimings(cat.CategoryTimings)

	return common.Category{
		ID:            &id,
		ProviderId:    &providerId,
		Name:          &cat.CategoryName,
		Description:   &cat.Description,
		ImageURL:      &cat.CategoryImageURL,
		SortOrder:     sortOrder,
		Subcategories: transformSubcategories(cat.Subcategories),
		Items:         make([]common.Item, 0),
		Timings:       timingID,
	}
}

func transformSubcategories(subcategories []petpooja.Category) []common.Category {
	if len(subcategories) == 0 {
		return nil
	}

	commonSubcategories := make([]common.Category, 0, len(subcategories))
	for _, subcat := range subcategories {
		commonSubcategories = append(commonSubcategories, transformCategory(subcat))
	}

	return commonSubcategories
}

func transformAllergenInfo(info *petpooja.AllergenInfo) *common.AllergenInfo {
	if info == nil {
		return nil
	}

	return &common.AllergenInfo{
		Allergen:     info.Allergen,
		AllergenDesc: info.AllergenDesc,
	}
}

func transformAdditionalInfo(info *petpooja.AdditionalInfo) *common.AdditionalInfo {
	if info == nil {
		return nil
	}

	return &common.AdditionalInfo{
		Info:   info.Info,
		Remark: info.Remark,
	}
}

func transformNutritionalInfo(info *petpooja.NutritionalInfo) *common.NutritionalInfo {
	if info == nil {
		return nil
	}

	// Transform AdditiveMap
	additiveMap := make(map[string]common.NutritionalValue)
	if info.AdditiveMap != nil {
		for key, value := range info.AdditiveMap {
			additiveMap[key] = common.NutritionalValue{
				Value: value.Amount,
				Unit:  value.Unit,
				Name:  value.Name,
			}
		}
	}

	return &common.NutritionalInfo{
		Carbohydrate:   transformNutritionalValue(info.Carbohydrate),
		Fiber:          transformNutritionalValue(info.Fiber),
		Protein:        transformNutritionalValue(info.Protein),
		Calorie:        transformNutritionalValue(info.Calories),
		Sodium:         transformNutritionalValue(info.Sodium),
		Cholesterol:    transformNutritionalValue(info.Cholesterol),
		FoodAmount:     transformNutritionalValue(info.FoodAmount),
		Minerals:       transformNutritionalValue(info.Minerals),
		TotalSugar:     transformNutritionalValue(info.TotalSugar),
		AddedSugar:     transformNutritionalValue(info.AddedSugar),
		TotalFat:       transformNutritionalValue(info.TotalFat),
		SaturatedFat:   transformNutritionalValue(info.SaturatedFat),
		TransFat:       transformNutritionalValue(info.TransFat),
		Vitamins:       transformNutritionalValue(info.Vitamins),
		ServingInfo:    info.ServingInfo,
		AdditiveMap:    additiveMap,
		Allergens:      transformAllergenInfo(info.Allergens),
		AdditionalInfo: transformAdditionalInfo(info.AdditionalInfo),
	}
}

func transformNutritionalValue(value *petpooja.NutritionalValue) *common.NutritionalValue {
	if value == nil {
		return nil
	}

	var val *float64
	if value.Amount != nil {
		val = value.Amount
	}

	return &common.NutritionalValue{
		Value: val,
		Unit:  value.Unit,
		Name:  value.Name,
	}
}

func transformFulfillmentModes(modes []string, itemOrderType string, orderTypes []petpooja.OrderType) []string {
	// Create a map of orderTypeID to orderType for quick lookup
	orderTypeMap := make(map[int]string)
	for _, ot := range orderTypes {
		orderTypeMap[ot.OrderTypeID] = ot.OrderType
	}

	// Create a set to store unique fulfillment modes
	fulfillmentModeSet := make(map[string]bool)

	// Process FulfillmentModes array if present
	if len(modes) > 0 {
		for _, mode := range modes {
			if internalMode, ok := constants.PetPoojaFulfillmentModesMapToInternalMode[mode]; ok {
				fulfillmentModeSet[internalMode] = true
			}
		}
	}

	// Process ItemOrderType string if present
	if itemOrderType != "" {
		// Split the comma-separated order type IDs
		orderTypeIDs := strings.Split(strings.ReplaceAll(itemOrderType, " ", ""), ",")

		// Convert each ID to int and look up the order type
		for _, idStr := range orderTypeIDs {
			if id, err := strconv.Atoi(idStr); err == nil {
				if orderType, exists := orderTypeMap[id]; exists {
					// Map the order type to internal mode
					if internalMode, ok := constants.PetPoojaFulfillmentModesMapToInternalMode[orderType]; ok {
						fulfillmentModeSet[internalMode] = true
					}
				}
			}
		}
	}

	// Convert the set to a slice
	fulfillmentModes := make([]string, 0, len(fulfillmentModeSet))
	for mode := range fulfillmentModeSet {
		fulfillmentModes = append(fulfillmentModes, mode)
	}

	return fulfillmentModes
}

func transformTimings(timings []petpooja.Timing) []common.Timing {
	if len(timings) == 0 {
		return nil
	}

	commonTimings := make([]common.Timing, 0, len(timings))
	for _, timing := range timings {
		id := timing.ID
		commonTiming := common.Timing{
			ID:   &id,
			Days: transformDays(timing.Days),
		}
		commonTimings = append(commonTimings, commonTiming)
	}

	return commonTimings
}

func transformDays(days []petpooja.Day) []common.Day {
	if len(days) == 0 {
		return nil
	}

	commonDays := make([]common.Day, 0, len(days))
	for _, day := range days {
		dayVal := day.Day
		commonDay := common.Day{
			Day:   &dayVal,
			Slots: transformSlots(day.Slots),
		}
		commonDays = append(commonDays, commonDay)
	}

	return commonDays
}

func transformSlots(slots []petpooja.Slot) []common.Slot {
	if len(slots) == 0 {
		return nil
	}

	commonSlots := make([]common.Slot, 0, len(slots))
	for _, slot := range slots {
		startTime := slot.StartTime
		endTime := slot.EndTime
		commonSlot := common.Slot{
			StartTime: &startTime,
			EndTime:   &endTime,
		}
		commonSlots = append(commonSlots, commonSlot)
	}

	return commonSlots
}

func randomInt(max int) int {
	return rand.Intn(max)
}

func transformItem(item petpooja.Item, restaurant petpooja.Restaurant, addonGroups map[string]petpooja.AddonGroup, menuTaxes []petpooja.Tax, orderTypes []petpooja.OrderType, attributes []petpooja.Attribute, itemPackingChargeMap map[string]string) common.Item {
	price, _ := strconv.ParseFloat(item.Price, 64)
	markupPrice, _ := strconv.ParseFloat(item.MarkupPrice, 64)
	inStock := item.InStock == "1"
	recommended := item.IsRecommend == "1"
	sortOrder := 0
	if item.ItemRank != "" {
		sortOrder, _ = strconv.Atoi(item.ItemRank)
	}

	// Transform food type
	foodType := "not Specified" // Default value
	if item.ItemAttributeID != "" {
		// Find the attribute in the Attributes array
		for _, attr := range attributes {
			if attr.AttributeID == item.ItemAttributeID {
				// Map the attribute value to internal value
				if internalType, ok := constants.PetPoojaFoodTypesMapToInternal[attr.Attribute]; ok {
					foodType = internalType
				}
				break
			}
		}
	}

	// Transform variant group IDs
	variantGroupIDs := make([]string, 0)
	if len(item.Variation) > 0 {
		// Create a map to track unique variant group IDs
		groupIDMap := make(map[string]bool)
		for _, itemVariation := range item.Variation {
			if !groupIDMap[itemVariation.VariationID] {
				providerId := itemVariation.VariationID
				id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId
				variantGroupIDs = append(variantGroupIDs, id)
				groupIDMap[itemVariation.VariationID] = true
			}
		}
	}

	// Transform addon group IDs
	addOnGroupIDs := make([]string, 0)
	for _, itemAddon := range item.Addon {
		if _, exists := addonGroups[itemAddon.AddonGroupID]; exists {
			providerId := itemAddon.AddonGroupID
			id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId
			addOnGroupIDs = append(addOnGroupIDs, id)
		}
	}

	// Transform fulfillment modes
	fulfillmentModes := transformFulfillmentModes(item.FulfillmentModes, item.ItemOrderType, orderTypes)

	// Transform bill components
	billComponents := &common.ItemBillComponents{
		TaxIDs:  make([]string, 0),
		Charges: make([]string, 0),
	}
	if item.ItemTax != "" {
		taxIDs := strings.Split(item.ItemTax, ",")
		for _, taxID := range taxIDs {
			providerId := taxID
			id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId
			billComponents.TaxIDs = append(billComponents.TaxIDs, id)
		}
	}

	if item.ItemPackingCharges != "" {
		packingCharge, _ := strconv.ParseFloat(item.ItemPackingCharges, 64)
		// Only add packing charge if value is greater than zero
		if packingCharge > 0 {
			// Use the consistent packing charge ID from the map
			if chargeId, exists := itemPackingChargeMap[item.ItemID]; exists {
				billComponents.Charges = append(billComponents.Charges, chargeId)
				if restaurant.Details.PackagingApplicableOn == "ITEM" {
					taxIDs := strings.Split(item.ItemTax, ",")
					for _, taxID := range taxIDs {
						providerId := taxID
						id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId
						billComponents.TaxIDs = append(billComponents.TaxIDs, id)
					}
				}
			}
		}
	}

	providerId := item.ItemID
	id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

	// Transform category and subcategory IDs
	var categoryID, subcategoryID *string
	if item.ItemCategoryID != "" {
		catProviderId := item.ItemCategoryID
		catId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + catProviderId
		categoryID = &catId
	}

	return common.Item{
		ID:               &id,
		ProviderId:       &providerId,
		Name:             &item.ItemName,
		Description:      &item.ItemDescription,
		ImageURL:         &item.ItemImageURL,
		Price:            &price,
		MarkupPrice:      &markupPrice,
		InStock:          &inStock,
		Recommended:      &recommended,
		FoodType:         &foodType,
		Tags:             item.ItemTags,
		VariantGroupIDs:  variantGroupIDs,
		AddOnGroupIDs:    addOnGroupIDs,
		NutritionalInfo:  transformNutritionalInfo(item.NutritionalInfo),
		BillComponents:   billComponents,
		FulfillmentModes: fulfillmentModes,
		SortOrder:        sortOrder,
		CategoryID:       categoryID,
		SubcategoryID:    subcategoryID,
		MenuSharingCode:  &restaurant.Details.MenuSharingCode,
	}
}

// transformAddonGroupsToRoot transforms addon groups to root level
func transformAddonGroupsToRoot(addonGroups []petpooja.AddonGroup, attributes []petpooja.Attribute, items []petpooja.Item) []common.AddOnGroup {
	commonAddonGroups := make([]common.AddOnGroup, 0)

	// Create a map to store min/max values for each addon group
	addonGroupLimits := make(map[string]struct {
		min *int
		max *int
	})

	// First pass: collect all min/max values for each addon group
	for _, group := range addonGroups {
		// Initialize with nil values
		addonGroupLimits[group.AddonGroupID] = struct {
			min *int
			max *int
		}{min: nil, max: nil}

		// Look through all items to find min/max values
		for _, item := range items {
			for _, itemAddon := range item.Addon {
				if itemAddon.AddonGroupID == group.AddonGroupID {
					if itemAddon.AddonItemSelectionMin != "" {
						min, _ := strconv.Atoi(itemAddon.AddonItemSelectionMin)
						addonGroupLimits[group.AddonGroupID] = struct {
							min *int
							max *int
						}{min: &min, max: nil}
					}
					if itemAddon.AddonItemSelectionMax != "" {
						max, _ := strconv.Atoi(itemAddon.AddonItemSelectionMax)
						limits := addonGroupLimits[group.AddonGroupID]
						limits.max = &max
						addonGroupLimits[group.AddonGroupID] = limits
					}
					break
				}
			}
		}
	}

	// Second pass: create addon groups with the collected min/max values
	for _, group := range addonGroups {
		providerId := group.AddonGroupID
		id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

		limits := addonGroupLimits[group.AddonGroupID]

		commonAddonGroup := common.AddOnGroup{
			ID:             &id,
			ProviderId:     &providerId,
			Name:           &group.AddonGroupName,
			MinimumNeeded:  limits.min,
			MaximumAllowed: limits.max,
			AddOns:         transformAddons(group.AddonGroupItems, attributes),
		}
		commonAddonGroups = append(commonAddonGroups, commonAddonGroup)
	}
	return commonAddonGroups
}

// transformAddonGroups transforms addon groups for an item
func transformAddonGroups(itemAddons []petpooja.ItemAddon, addonGroupsMap map[string]petpooja.AddonGroup, attributes []petpooja.Attribute) []common.AddOnGroup {
	addOnGroups := make([]common.AddOnGroup, 0)

	for _, itemAddon := range itemAddons {
		if group, exists := addonGroupsMap[itemAddon.AddonGroupID]; exists {
			minItems, _ := strconv.Atoi(itemAddon.AddonItemSelectionMin)
			maxItems, _ := strconv.Atoi(itemAddon.AddonItemSelectionMax)

			providerId := group.AddonGroupID
			id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

			addOnGroups = append(addOnGroups, common.AddOnGroup{
				ID:             &id,
				ProviderId:     &providerId,
				Name:           &group.AddonGroupName,
				MinimumNeeded:  &minItems,
				MaximumAllowed: &maxItems,
				AddOns:         transformAddons(group.AddonGroupItems, attributes),
			})
		}
	}

	return addOnGroups
}

// transformAddons transforms addon items to common format
func transformAddons(addonItems []petpooja.AddonItem, attributes []petpooja.Attribute) []common.AddOn {
	commonAddons := make([]common.AddOn, 0)
	for _, addon := range addonItems {
		price, _ := strconv.ParseFloat(addon.AddonItemPrice, 64)
		inStock := addon.Active == "1"
		sortOrder, _ := strconv.Atoi(addon.AddonItemRank)

		providerId := addon.AddonItemID
		id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

		// Transform food type
		foodType := "not Specified" // Default value
		if addon.Attributes != "" {
			// Find the attribute in the Attributes array
			for _, attr := range attributes {
				if attr.AttributeID == addon.Attributes {
					// Map the attribute value to internal value
					if internalType, ok := constants.PetPoojaFoodTypesMapToInternal[attr.Attribute]; ok {
						foodType = internalType
					}
					break
				}
			}
		}

		commonAddon := common.AddOn{
			ID:               &id,
			ProviderId:       &providerId,
			Name:             &addon.AddonItemName,
			Price:            &price,
			InStock:          &inStock,
			FoodType:         &foodType,
			NutritionalInfo:  nil, // PetPooja doesn't have nutritional info for addons
			FulfillmentModes: transformFulfillmentModes(addon.FulfillmentModes, "", nil),
			SortOrder:        sortOrder,
		}
		commonAddons = append(commonAddons, commonAddon)
	}
	return commonAddons
}

func transformTax(tax petpooja.Tax) common.Tax {
	taxValue, _ := strconv.ParseFloat(tax.Tax, 64)
	taxName := tax.TaxName
	taxDesc := "Tax"

	// Set fulfillment modes based on TaxOrderType
	fulfillmentModes := make([]string, 0)

	// If TaxOrderType is 0 or empty, apply to all order types
	if tax.TaxOrderType == "" || tax.TaxOrderType == constants.PetpoojaOrderTypeIDEmpty {
		fulfillmentModes = append(fulfillmentModes,
			constants.FulfillmentModeDeliveryInternal,
			constants.FulfillmentModePickupInternal,
			constants.FulfillmentModeDineInInternal)
	} else {
		// Parse comma-separated tax order types
		taxOrderTypes := strings.Split(tax.TaxOrderType, ",")
		for _, orderType := range taxOrderTypes {
			// Map OrderType to FulfillmentMode
			switch orderType {
			case constants.PetpoojaOrderTypeIDDelivery: // Delivery
				fulfillmentModes = append(fulfillmentModes, constants.FulfillmentModeDeliveryInternal)
			case constants.PetpoojaOrderTypeIDPickup: // Pickup
				fulfillmentModes = append(fulfillmentModes, constants.FulfillmentModePickupInternal)
			case constants.PetpoojaOrderTypeIDDineIn: // Dine-in
				fulfillmentModes = append(fulfillmentModes, constants.FulfillmentModeDineInInternal)
			}
		}
	}

	providerId := tax.TaxID
	id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId

	return common.Tax{
		ID:               &id,
		ProviderId:       &providerId,
		Name:             &taxName,
		Description:      &taxDesc,
		Value:            &taxValue,
		FulfillmentModes: fulfillmentModes,
	}
}

func (t *transformersImpl) transformRestaurant(restaurant petpooja.Restaurant) *petpooja.Restaurant {
	return &restaurant
}

// generateTimingID generates a simple hash for a timing string
func generateTimingID(timingStr string) string {
	// Simple hash function that returns a number
	hash := 0
	for i := 0; i < len(timingStr); i++ {
		hash = 31*hash + int(timingStr[i])
	}
	// Convert to positive number and take modulo to keep it reasonable length
	if hash < 0 {
		hash = -hash
	}
	return fmt.Sprintf("%d", hash%1000000) // Keep it to 6 digits max
}

func transformPetPoojaTimings(timingsStr string) ([]common.Timing, *string) {
	if timingsStr == "" {
		return nil, nil
	}

	var petPoojaTimings []struct {
		ScheduleName      string `json:"schedule_name"`
		ScheduleDay       string `json:"schedule_day"`
		ScheduleTimeSlots []struct {
			StartTime string `json:"start_time"`
			EndTime   string `json:"end_time"`
		} `json:"schedule_time_slots"`
	}

	err := json.Unmarshal([]byte(timingsStr), &petPoojaTimings)
	if err != nil {
		return nil, nil
	}

	// Generate a single timing ID for all timings in this string
	providerId := generateTimingID(timingsStr)
	id := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + providerId
	commonTimings := make([]common.Timing, 0)

	for _, timing := range petPoojaTimings {
		// Create a map to group slots by day
		daySlotsMap := make(map[string][]common.Slot)

		// Process time slots
		for _, slot := range timing.ScheduleTimeSlots {
			startTime := slot.StartTime
			endTime := slot.EndTime
			commonSlot := common.Slot{
				StartTime: &startTime,
				EndTime:   &endTime,
			}

			// Handle "All" days case
			if timing.ScheduleDay == constants.DayAll {
				days := []string{constants.DayMonday, constants.DayTuesday, constants.DayWednesday, constants.DayThursday, constants.DayFriday, constants.DaySaturday, constants.DaySunday}
				for _, day := range days {
					daySlotsMap[day] = append(daySlotsMap[day], commonSlot)
				}
			} else {
				// Handle comma-separated days
				days := strings.Split(timing.ScheduleDay, ",")
				for _, day := range days {
					day = strings.TrimSpace(day)
					// Convert short day names to full names
					switch day {
					case constants.DayMon:
						day = constants.DayMonday
					case constants.DayTue:
						day = constants.DayTuesday
					case constants.DayWed:
						day = constants.DayWednesday
					case constants.DayThu:
						day = constants.DayThursday
					case constants.DayFri:
						day = constants.DayFriday
					case constants.DaySat:
						day = constants.DaySaturday
					case constants.DaySun:
						day = constants.DaySunday
					}
					daySlotsMap[day] = append(daySlotsMap[day], commonSlot)
				}
			}
		}

		// Create days with their slots
		days := make([]common.Day, 0)
		for day, slots := range daySlotsMap {
			dayVal := day
			commonDay := common.Day{
				Day:   &dayVal,
				Slots: slots,
			}
			days = append(days, commonDay)
		}

		commonTiming := common.Timing{
			ID:   &id,
			Days: days,
		}
		commonTimings = append(commonTimings, commonTiming)
	}

	// Return both the timings array and the timing ID for the category
	if len(commonTimings) > 0 {
		return commonTimings, &id
	}

	return nil, nil
}

// transformVariantGroupsAndVariantsToRoot transforms variant groups and variants to root level
func transformVariantGroupsAndVariantsToRoot(items []petpooja.Item, restaurant petpooja.Restaurant) ([]common.VariantGroup, []common.Variant) {
	variantGroups := make([]common.VariantGroup, 0)
	variants := make([]common.Variant, 0)

	// Maps to track unique variant groups and variants
	variantGroupMap := make(map[string]bool)
	variantMap := make(map[string]bool)

	// Map to track item attribute ID to food type
	attributeFoodTypeMap := make(map[string]string)

	// Process all items to extract variant groups, variants, and build the attribute map
	for _, item := range items {
		if item.ItemAttributeID != "" {
			attributeFoodTypeMap[item.ItemAttributeID] = item.ItemAttribute
		}

		// Process all variations for this item
		for _, variation := range item.Variation {
			// Add variant group if not already added
			if !variantGroupMap[variation.VariationID] {
				groupProviderId := variation.VariationID
				groupId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + groupProviderId

				// Collect all variants for this group
				variantIds := make([]string, 0)
				for _, v := range item.Variation {
					if v.VariationID == variation.VariationID {
						variantProviderId := v.ID
						variantId := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + variantProviderId
						variantIds = append(variantIds, variantId)
					}
				}

				// Add group to the result
				variantGroups = append(variantGroups, common.VariantGroup{
					ID:         &groupId,
					ProviderId: &groupProviderId,
					Name:       &variation.GroupName,
					VariantIDs: variantIds,
				})

				variantGroupMap[variation.VariationID] = true
			}

			// Add variant if not already added
			if !variantMap[variation.ID] {
				// Determine food type
				foodType := "not Specified" // Default
				if item.ItemAttributeID != "" {
					if attr, ok := attributeFoodTypeMap[item.ItemAttributeID]; ok {
						if internalType, ok := constants.PetPoojaFoodTypesMapToInternal[attr]; ok {
							foodType = internalType
						}
					}
				}

				// Create packing charge ID for variant if needed
				var packingChargeId string
				if item.ItemID == "10567686" {
					fmt.Println("")
				}
				if variation.ItemPackingCharges != "" {
					packingCharge, _ := strconv.ParseFloat(variation.ItemPackingCharges, 64)
					// Only add if charge value is greater than zero
					if packingCharge > 0 {
						// Use the packing charge value as ID to keep identical charges consistent
						packingChargeId = constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + "PC" + variation.ItemPackingCharges
					}
				}

				// Transform variant
				variantId := variation.ID
				idWithPrefix := constants.ProviderAbbreviationMap[constants.PetpoojaProvider] + variantId
				inStock := true
				variant := common.Variant{
					ID:               &idWithPrefix,
					ProviderId:       &variantId,
					Name:             &variation.Name,
					Price:            nil,
					InStock:          &inStock,
					FoodType:         &foodType,
					AddOnGroupIDs:    nil,
					FulfillmentModes: nil,
				}

				// Add price if available
				if variation.Price != "" {
					if price, err := strconv.ParseFloat(variation.Price, 64); err == nil {
						variant.Price = &price
					}
				}

				// Add markup price if available
				if variation.MarkupPrice != "" {
					if markupPrice, err := strconv.ParseFloat(variation.MarkupPrice, 64); err == nil {
						variant.MarkupPrice = &markupPrice
					}
				}

				// Add fulfillment modes if available
				if len(variation.FulfillmentModes) > 0 {
					variant.FulfillmentModes = transformFulfillmentModes(variation.FulfillmentModes, "", nil)
				}

				// Add addon group IDs if available
				if len(variation.Addon) > 0 {
					addOnGroupIDs := make([]string, 0)
					for _, addon := range variation.Addon {
						addOnGroupIDs = append(addOnGroupIDs, constants.ProviderAbbreviationMap[constants.PetpoojaProvider]+addon.AddonGroupID)
					}
					variant.AddOnGroupIDs = addOnGroupIDs
				}

				// Add bill components if packing charge exists
				if packingChargeId != "" {
					variant.BillComponents = &common.ItemBillComponents{
						Charges: []string{packingChargeId},
					}
				}

				variants = append(variants, variant)
				variantMap[variation.ID] = true
			}
		}
	}

	return variantGroups, variants
}

func (t *transformersImpl) TransformPetpoojaInventory(inventory *map[string]interface{}) *common.InventoryUpdate {
	var petpoojaInventory petpooja.InventoryUpdate
	err := utils.UnmarshalJSONToInterface(inventory, &petpoojaInventory)
	if err != nil {
		return nil
	}

	commonInventory := &common.InventoryUpdate{
		MenuSharingCode: &petpoojaInventory.RestID,
		InStock:         petpoojaInventory.InStock,
	}

	// Handle different types (addon/item)
	if petpoojaInventory.Type == "addon" {
		commonInventory.AddOns = petpoojaInventory.ItemID
	} else {
		commonInventory.Items = petpoojaInventory.ItemID
	}

	return commonInventory
}

func (t *transformersImpl) TransformPetpoojaProviderStoreIntoUnifiedStoreStatus(storeStatus *map[string]interface{}) *common.StoreStatus {
	var petpoojaStoreStatus petpooja.StoreStatus
	err := utils.UnmarshalJSONToInterface(storeStatus, &petpoojaStoreStatus)
	if err != nil {
		return nil
	}

	commonStoreStatus := &common.StoreStatus{
		MenuSharingCode: petpoojaStoreStatus.RestID,
		OrderingEnabled: petpoojaStoreStatus.StoreStatus == 1, // Convert "1" to true, anything else to false
		//NextAvailableAt: petpoojaStoreStatus.TurnOnTime,
		Reason: petpoojaStoreStatus.Reason,
	}

	return commonStoreStatus
}

// TransformUnifiedOrderToPetpoojaOrder converts unified order format to Petpooja order format
func (t *transformersImpl) TransformUnifiedOrderToPetpoojaOrder(order *orders.Order) interface{} {
	if order == nil {
		return nil
	}

	// Transform to Petpooja order structure using proper structs
	return transformToPetpoojaOrderStruct(order)
}

// transformToPetpoojaOrderStruct transforms unified order to Petpooja order structure using proper structs
func transformToPetpoojaOrderStruct(order *orders.Order) interface{} {
	// Use constants for payment and delivery types
	// Derive order type from delivery mode since Type field was removed
	orderType := transformPetpoojaOrderTypeFromDeliveryMode(order.OrderInfo.DeliveryMode)
	paymentType := transformPetpoojaPaymentType(order.Payment.Mode)

	// Calculate delivery and packing charges from order charges
	deliveryCharge, packingCharge := calculateChargesFromOrder(order)

	// Calculate amount balance (total - amount paid)
	amountBalance := order.OrderInfo.Total - order.Payment.AmountPaid

	// Create the Petpooja order request using proper structs
	petpoojaOrder := petpoojaTypes.SaveOrderRequest{
		AppKey:             "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",         // This should come from config
		AppSecret:          "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", // This should come from config
		AccessToken:        "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", // This should come from config
		ResName:            "Restaurant_" + order.OrderInfo.RestID,     // Derive from RestID since Restaurant field was removed
		Address:            "",                                         // This would need to be provided or looked up
		ContactInformation: "",                                         // This would need to be provided or looked up
		RestID:             order.OrderInfo.RestID,
		OrderInfo: petpoojaTypes.OrderInfo{
			Customer: petpoojaTypes.Customer{
				Email:     "", // Not available in unified order
				Name:      order.Customer.FirstName + " " + order.Customer.LastName,
				Address:   "", // Not available in unified order
				Phone:     "", // Not available in unified order
				Latitude:  "", // Not available in unified order
				Longitude: "", // Not available in unified order
			},
			Order: petpoojaTypes.Order{
				OrderID:         order.OrderInfo.OrderID,
				PreorderDate:    time.Unix(int64(order.OrderInfo.CreatedAt), 0).Format("2006-01-02"),
				PreorderTime:    time.Unix(int64(order.OrderInfo.CreatedAt), 0).Format("15:04:05"),
				ServiceCharge:   "0",
				SCTaxAmount:     "0",
				DeliveryCharges: fmt.Sprintf("%.2f", deliveryCharge),
				DCTaxAmount:     "0",
				DCGSTDetails:    []petpoojaTypes.GSTDetail{},
				PackingCharges:  fmt.Sprintf("%.2f", packingCharge),
				PCTaxAmount:     "0",
				PCGSTDetails:    []petpoojaTypes.GSTDetail{},
				OrderType:       orderType,
				ONDCBap:         "",
				AdvancedOrder:   "N",
				UrgentOrder:     false,
				UrgentTime:      0,
				PaymentType:     paymentType,
				TableNo:         "",
				NoOfPersons:     "0",
				DiscountTotal:   "0",
				Discount:        "0",
				DiscountType:    "F",
				Total:           fmt.Sprintf("%.2f", order.OrderInfo.Total),
				TaxTotal:        fmt.Sprintf("%.2f", order.OrderInfo.TotalTaxes),
				Description:     order.OrderInfo.Instruction,
				CreatedOn:       time.Unix(int64(order.OrderInfo.CreatedAt), 0).Format("2006-01-02 15:04:05"),
				EnableDelivery:  1,
				MinPrepTime:     0,
				CallbackURL:     "",
				CollectCash:     fmt.Sprintf("%.2f", amountBalance),
				OTP:             "",
			},
			OrderItem: transformPetpoojaOrderItemsStruct(order.Items),
			Tax:       transformPetpoojaOrderTaxesStruct(order.Items),
			Discount:  []petpoojaTypes.Discount{}, // Empty discount array
		},
		UDID:       "",
		DeviceType: "Web",
	}

	return petpoojaOrder
}

// transformPetpoojaOrderItemsStruct transforms unified order items to Petpooja format using structs
func transformPetpoojaOrderItemsStruct(items []orders.OrderItem) []petpoojaTypes.OrderItem {
	orderItems := []petpoojaTypes.OrderItem{}

	for _, item := range items {
		// Transform item taxes
		itemTaxes := []petpoojaTypes.ItemTax{}
		for _, tax := range item.Taxes {
			itemTaxes = append(itemTaxes, petpoojaTypes.ItemTax{
				ID:     generateTaxID(tax.Title), // Generate a unique ID for the tax
				Name:   tax.Title,
				Amount: fmt.Sprintf("%.2f", tax.Value),
			})
		}

		// Add taxes from variants and add-ons (flattened for Petpooja)
		variantTaxes := flattenVariantsTaxes(item.Variants, item.Quantity)
		for _, tax := range variantTaxes {
			itemTaxes = append(itemTaxes, petpoojaTypes.ItemTax{
				ID:     generateTaxID(tax.Title),
				Name:   tax.Title,
				Amount: fmt.Sprintf("%.2f", tax.Value),
			})
		}

		addOnTaxes := flattenAddOnTaxes(item.AddOns, item.Quantity)
		for _, tax := range addOnTaxes {
			itemTaxes = append(itemTaxes, petpoojaTypes.ItemTax{
				ID:     generateTaxID(tax.Title),
				Name:   tax.Title,
				Amount: fmt.Sprintf("%.2f", tax.Value),
			})
		}

		// Transform addon items (flattened for Petpooja)
		addonItems := []petpoojaTypes.AddonItem{}
		flattenedAddOns := flattenAddOns(item.AddOns)
		for _, addon := range flattenedAddOns {
			addonItems = append(addonItems, petpoojaTypes.AddonItem{
				ID:        addon.ID,
				Name:      addon.Name,
				GroupName: "Add-ons", // Default group name
				Price:     fmt.Sprintf("%.2f", addon.UnitPrice),
				GroupID:   "1", // Default group ID as string
				Quantity:  "1",
			})
		}

		// Add flattened variant add-ons
		if len(item.Variants) > 0 {
			variantAddOns := flattenVariantsAddOns(item.Variants)
			for _, addon := range variantAddOns {
				addonItems = append(addonItems, petpoojaTypes.AddonItem{
					ID:        addon.ID,
					Name:      addon.Name,
					GroupName: "Variant Add-ons",
					Price:     fmt.Sprintf("%.2f", addon.UnitPrice),
					GroupID:   "2", // Different group ID for variant add-ons as string
					Quantity:  "1",
				})
			}
		}

		// Calculate final price (unit price * quantity - discount)
		finalPrice := item.UnitPrice * float64(item.Quantity)

		// Transform variants if available (use first variant for Petpooja since it doesn't support multiple)
		variationName := ""
		variationID := ""
		if len(item.Variants) > 0 {
			variationName = item.Variants[0].Name
			variationID = item.Variants[0].ID
		}

		// Determine GST liability from taxes or default to vendor
		gstLiability := constants.TaxLiabilityVendor
		for _, tax := range item.Taxes {
			if tax.LiabilityOn != "" {
				gstLiability = tax.LiabilityOn
				break
			}
		}

		orderItem := petpoojaTypes.OrderItem{
			ID:            item.ItemID,
			Name:          item.Name,
			GSTLiability:  gstLiability,
			ItemTax:       itemTaxes,
			ItemDiscount:  "0", // No discount in unified order
			Price:         fmt.Sprintf("%.2f", item.UnitPrice),
			FinalPrice:    fmt.Sprintf("%.2f", finalPrice),
			Quantity:      fmt.Sprintf("%d", item.Quantity),
			Description:   item.Name, // Use item name as description since Description field was removed
			VariationName: variationName,
			VariationID:   variationID,
			AddonItem:     addonItems,
		}

		orderItems = append(orderItems, orderItem)
	}

	return orderItems
}

// transformPetpoojaOrderTaxesStruct transforms unified order taxes to Petpooja format using structs
func transformPetpoojaOrderTaxesStruct(items []orders.OrderItem) []petpoojaTypes.Tax {
	taxes := []petpoojaTypes.Tax{}
	taxMap := make(map[string]float64)         // To aggregate taxes by name
	taxLiabilityMap := make(map[string]string) // To track liability for each tax

	// Aggregate taxes from all items
	for _, item := range items {
		// Add item taxes
		for _, tax := range item.Taxes {
			if existingAmount, exists := taxMap[tax.Title]; exists {
				taxMap[tax.Title] = existingAmount + (tax.Value * float64(item.Quantity))
			} else {
				taxMap[tax.Title] = tax.Value * float64(item.Quantity)
				// Store liability for this tax
				if tax.LiabilityOn != "" {
					taxLiabilityMap[tax.Title] = tax.LiabilityOn
				} else {
					taxLiabilityMap[tax.Title] = constants.TaxLiabilityRestaurant
				}
			}
		}

		// Add taxes from variants and add-ons (flattened)
		variantTaxes := flattenVariantsTaxes(item.Variants, item.Quantity)
		for _, tax := range variantTaxes {
			if existingAmount, exists := taxMap[tax.Title]; exists {
				taxMap[tax.Title] = existingAmount + tax.Value
			} else {
				taxMap[tax.Title] = tax.Value
				if tax.LiabilityOn != "" {
					taxLiabilityMap[tax.Title] = tax.LiabilityOn
				} else {
					taxLiabilityMap[tax.Title] = constants.TaxLiabilityRestaurant
				}
			}
		}

		addOnTaxes := flattenAddOnTaxes(item.AddOns, item.Quantity)
		for _, tax := range addOnTaxes {
			if existingAmount, exists := taxMap[tax.Title]; exists {
				taxMap[tax.Title] = existingAmount + tax.Value
			} else {
				taxMap[tax.Title] = tax.Value
				if tax.LiabilityOn != "" {
					taxLiabilityMap[tax.Title] = tax.LiabilityOn
				} else {
					taxLiabilityMap[tax.Title] = constants.TaxLiabilityRestaurant
				}
			}
		}
	}

	// Convert aggregated taxes to Petpooja format
	for taxName, totalAmount := range taxMap {
		liability := taxLiabilityMap[taxName]
		restaurantLiableAmt := "0"

		// If restaurant is liable, set the amount
		if liability == constants.TaxLiabilityRestaurant || liability == constants.TaxLiabilityVendor {
			restaurantLiableAmt = fmt.Sprintf("%.2f", totalAmount)
		}

		tax := petpoojaTypes.Tax{
			ID:                  generateTaxID(taxName),
			Title:               taxName,
			Type:                "P", // Percentage type
			Price:               "0", // Tax percentage (not available in unified order)
			Tax:                 fmt.Sprintf("%.2f", totalAmount),
			RestaurantLiableAmt: restaurantLiableAmt,
		}
		taxes = append(taxes, tax)
	}

	return taxes
}

// Helper functions for struct-based transformation are defined below

// Helper functions for Petpooja order transformation

// transformPetpoojaOrderType transforms unified order type to Petpooja order type
func transformPetpoojaOrderType(orderType string) string {
	switch strings.ToLower(orderType) {
	case constants.DeliveryTypeDelivery:
		return constants.PetpoojaOrderTypeDelivery
	case constants.DeliveryTypeTakeaway:
		return constants.PetpoojaOrderTypeTakeaway
	case constants.DeliveryTypeDineIn:
		return constants.PetpoojaOrderTypeDineIn
	default:
		return constants.PetpoojaOrderTypeDelivery // Default to home delivery
	}
}

// transformPetpoojaOrderTypeFromDeliveryMode transforms delivery mode to Petpooja order type
func transformPetpoojaOrderTypeFromDeliveryMode(deliveryMode string) string {
	switch strings.ToLower(deliveryMode) {
	case "delivery":
		return constants.PetpoojaOrderTypeDelivery
	case "self_delivery":
		return constants.PetpoojaOrderTypeTakeaway
	default:
		return constants.PetpoojaOrderTypeDelivery // Default to home delivery
	}
}

// calculateChargesFromOrder calculates delivery and packing charges from order items
// For Petpooja: Use totalPackingCharge from unified order structure
func calculateChargesFromOrder(order *orders.Order) (deliveryCharge, packingCharge float64) {
	// Calculate delivery charges from all items
	for _, item := range order.Items {
		for _, charge := range item.Charges {
			switch strings.ToLower(charge.Title) {
			case "delivery charge", "delivery":
				deliveryCharge += charge.Value * float64(item.Quantity)
			}
		}
	}

	// For Petpooja: Always use totalPackingCharge from unified order structure
	packingCharge = order.OrderInfo.TotalPackingCharge

	return deliveryCharge, packingCharge
}

// transformPetpoojaPaymentType transforms unified payment mode to Petpooja payment type
func transformPetpoojaPaymentType(mode string) string {
	switch strings.ToLower(mode) {
	case constants.PaymentTypeCash:
		return constants.PetpoojaPaymentTypeCOD
	case constants.PaymentTypeCard, constants.PaymentTypeUPI, constants.PaymentTypeOnline:
		return constants.PetpoojaPaymentTypeOnline
	default:
		return constants.PetpoojaPaymentTypeCOD // Default to cash on delivery
	}
}

// generateTaxID generates a unique ID for tax based on tax name
func generateTaxID(taxName string) string {
	// Simple hash-based ID generation
	hash := 0
	for _, char := range taxName {
		hash = hash*31 + int(char)
	}
	if hash < 0 {
		hash = -hash
	}
	return fmt.Sprintf("%d", hash%100000) // Keep it within 5 digits
}

// flattenVariantsTaxes flattens taxes from array of variants for Petpooja
func flattenVariantsTaxes(variants []orders.Variant, quantity int) []orders.Tax {
	var taxes []orders.Tax
	for _, variant := range variants {
		variantTaxes := flattenVariantTaxes(&variant, quantity)
		taxes = append(taxes, variantTaxes...)
	}
	return taxes
}

// flattenVariantTaxes flattens taxes from nested variants for Petpooja
// Since Taxes field was removed from Variant, this function now returns empty slice
func flattenVariantTaxes(variant *orders.Variant, quantity int) []orders.Tax {
	if variant == nil {
		return []orders.Tax{}
	}

	var taxes []orders.Tax

	// Taxes field was removed from Variant struct in unified order structure
	// Add taxes from nested variants
	for _, nestedVariant := range variant.Variants {
		nestedTaxes := flattenVariantTaxes(&nestedVariant, quantity)
		taxes = append(taxes, nestedTaxes...)
	}

	// Add taxes from variant's add-ons (but AddOns don't have taxes anymore)
	addOnTaxes := flattenAddOnTaxes(variant.AddOns, quantity)
	taxes = append(taxes, addOnTaxes...)

	return taxes
}

// flattenAddOnTaxes flattens taxes from nested add-ons for Petpooja
// Since Taxes and nested AddOns fields were removed from AddOn, this function now returns empty slice
func flattenAddOnTaxes(addOns []orders.AddOn, quantity int) []orders.Tax {
	var taxes []orders.Tax

	// Taxes and nested AddOns fields were removed from AddOn struct in unified order structure
	// This function now returns empty slice as AddOns no longer have taxes or nested structure

	return taxes
}

// flattenAddOns flattens nested add-ons for Petpooja
// Since nested AddOns field was removed from AddOn, this function now just returns the input slice
func flattenAddOns(addOns []orders.AddOn) []orders.AddOn {
	// Nested AddOns field was removed from AddOn struct in unified order structure
	// Just return the input slice as there's no nesting anymore
	return addOns
}

// flattenVariantsAddOns flattens add-ons from array of variants for Petpooja
func flattenVariantsAddOns(variants []orders.Variant) []orders.AddOn {
	var addOns []orders.AddOn
	for _, variant := range variants {
		variantAddOns := flattenVariantAddOns(&variant)
		addOns = append(addOns, variantAddOns...)
	}
	return addOns
}

// flattenVariantAddOns flattens add-ons from nested variants for Petpooja
func flattenVariantAddOns(variant *orders.Variant) []orders.AddOn {
	if variant == nil {
		return []orders.AddOn{}
	}

	var addOns []orders.AddOn

	// Add add-ons from current variant
	addOns = append(addOns, variant.AddOns...)

	// Add add-ons from nested variants
	for _, nestedVariant := range variant.Variants {
		nestedAddOns := flattenVariantAddOns(&nestedVariant)
		addOns = append(addOns, nestedAddOns...)
	}

	// Flatten nested add-ons
	return flattenAddOns(addOns)
}
