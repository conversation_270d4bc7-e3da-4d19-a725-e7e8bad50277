package handler

import (
	"fmt"

	"github.com/gin-gonic/gin"
	posClientService "github.com/nutanalabs/pos-gateway/internal/posclients"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/service"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	"github.com/nutanalabs/pos-gateway/internal/validation/menu"
	ordervalidation "github.com/nutanalabs/pos-gateway/internal/validation/order"
	logger "github.com/roppenlabs/rapido-logger-go"
)

type Handler struct {
	service          service.Service
	utils            utils.Utils
	transformers     transformers.Transformers
	posClientService posClientService.POSClientService
}

func NewHandler(s service.Service, utils utils.Utils, transformers transformers.Transformers, posClientService posClientService.POSClientService) *Handler {
	return &Handler{
		service:          s,
		utils:            utils,
		transformers:     transformers,
		posClientService: posClientService,
	}
}

func (h *Handler) PushMenu(requestBody map[string]interface{}, clientID string, isASyncProcessing bool) (types.SuccessResponse, *types.StatusError) {

	fmt.Println(utils.ConvertInterfaceToJSON(requestBody))

	// Transform the menu into unified format
	unifiedMenu := h.transformers.TransformProviderMenuIntoUnifiedMenu(&requestBody, clientID)
	if unifiedMenu == nil {
		return types.SuccessResponse{}, types.NewValidationError("Failed to transform menu", nil)
	}

	if isASyncProcessing && (unifiedMenu.CallbackURL == "" || h.posClientService.Adapters[clientID] == nil) {
		return types.SuccessResponse{}, types.NewValidationError("Callback URL or POS client adapter not found", nil)
	}

	// Validate the unified menu
	validationErrors := menu.ValidateMenu(unifiedMenu)
	if len(*validationErrors) > 0 {
		errorData := map[string]interface{}{
			"errors": validationErrors,
		}
		statusError := types.NewValidationError("Invalid menu data", &errorData)
		if isASyncProcessing {
			err := h.handlerCallbackProcessing(clientID, unifiedMenu.CallbackURL, false, validationErrors)
			if err != nil {
				logger.Error(logger.Format{
					Message: "Error while sending menu processing status",
					Data: map[string]string{
						"clientID": clientID,
						"error":    utils.ConvertInterfaceToJSON(errorData),
					},
				})
			}
			return types.SuccessResponse{}, statusError
		}
		return types.SuccessResponse{}, statusError
	}

	serviceRequest := types.ServiceRequest{
		RawData:     &requestBody,
		UnifiedData: unifiedMenu,
		ClientID:    clientID,
	}

	res, err := h.service.PushMenu(serviceRequest)
	if err != nil {
		return types.SuccessResponse{}, types.NewInternalServerError(err.Error(), nil)
	}

	if isASyncProcessing {
		callbackErr := h.handlerCallbackProcessing(clientID, unifiedMenu.CallbackURL, true, validationErrors)
		if callbackErr != nil {
			logger.Error(logger.Format{
				Message: "Error while sending menu processing status",
				Data: map[string]string{
					"clientID": clientID,
					"error":    utils.ConvertInterfaceToJSON(callbackErr),
				},
			})
			return types.SuccessResponse{}, &types.StatusError{
				DisplayMessage: "Failed to send menu processing status",
				Message:        "Failed to send menu processing status to POS provider. Client ID: " + clientID,
				Code:           "error_sending_menu_processing_status",
			}
		}
	}

	return res, nil
}

func (h *Handler) PushItemInventoryStatus(context *gin.Context) {
	clientID := getXConsumerUserName(context)
	if clientID == "" {
		context.JSON(400, gin.H{"error": "Client ID is required"})
		return
	}

	var requestBody map[string]interface{}
	err := context.BindJSON(&requestBody)
	if err != nil {
		context.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	// Transform the inventory into unified format
	unifiedInventory := h.transformers.TransformProviderInventoryIntoUnifiedInventory(&requestBody, clientID)
	if unifiedInventory == nil {
		context.JSON(500, gin.H{"error": "Failed to transform inventory"})
		return
	}

	serviceRequest := types.ServiceRequest{
		RawData:     &requestBody,
		UnifiedData: unifiedInventory,
		ClientID:    clientID,
	}

	res, err := h.service.PushItemInventoryStatus(serviceRequest)
	if err != nil {
		context.JSON(500, gin.H{"error": "Failed to push item inventory status"})
		return
	}
	context.JSON(200, res)
}

func (h *Handler) PushRestaurantStatus(context *gin.Context) {
	clientID := getXConsumerUserName(context)
	if clientID == "" {
		context.JSON(400, gin.H{"error": "Client ID is required"})
		return
	}

	var requestBody map[string]interface{}
	err := context.BindJSON(&requestBody)
	if err != nil {
		context.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	// Transform the store status into unified format
	unifiedStoreStatus := h.transformers.TransformProviderStoreIntoUnifiedStoreStatus(&requestBody, clientID)
	if unifiedStoreStatus == nil {
		context.JSON(500, gin.H{"error": "Failed to transform store status"})
		return
	}

	serviceRequest := types.ServiceRequest{
		RawData:     &requestBody,
		UnifiedData: unifiedStoreStatus,
		ClientID:    clientID,
	}

	res, err := h.service.PushStoreStatus(serviceRequest)
	if err != nil {
		context.JSON(500, gin.H{"error": "Failed to push store status"})
		return
	}
	context.JSON(200, res)
}

func (h *Handler) PushOrder(context *gin.Context) {
	clientID := getXConsumerUserName(context)
	if clientID == "" {
		context.JSON(400, gin.H{"error": "Client ID is required"})
		return
	}

	var order orders.Order
	err := context.BindJSON(&order)
	if err != nil {
		context.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	// Validate the order
	validationErrors := ordervalidation.ValidateOrder(&order)
	if len(validationErrors) > 0 {
		errorData := map[string]interface{}{
			"errors": validationErrors,
		}
		statusError := types.StatusError{
			DisplayMessage: "Invalid request",
			Message:        "Invalid order request",
			Code:           "error_processing_request",
			Data:           &errorData,
		}
		context.JSON(400, statusError)
		return
	}

	res, err := h.service.PushOrder(&order, clientID)
	if err != nil {
		context.JSON(500, gin.H{"error": "Failed to push order"})
		return
	}
	context.JSON(200, res)
}
