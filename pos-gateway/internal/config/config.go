package config

import (
	"fmt"
	"strconv"

	"github.com/spf13/viper"
)

var AppConfig Config

type Config struct {
	Log              LogConfig
	Server           ServerConfig
	Kafka            KafkaConfig
	Mongo            MongoConfig
	Restroworks      RestroworksConfig
	Freshmenu        FreshmenuConfig
	Environment      string
	ProfilingEnabled bool
}

type LogConfig struct {
	Level string
}

type ServerConfig struct {
	Host string
	Port int
}

type APIPath struct {
	Path          string
	TimeoutInSecs int `mapstructure:"timeoutInSecs"`
}

type RestroworksConfig struct {
	BaseURL string `mapstructure:"baseURL"`
}

type FreshmenuConfig struct {
	BaseURL string `mapstructure:"baseURL"`
}

func InitDefaultConfig() *Config {
	return InitConfig("application")
}

func InitConfig(configname string) *Config {
	viper.AutomaticEnv()
	viper.SetConfigName(configname)
	viper.SetConfigType("yaml")
	viper.SetEnvPrefix("rapido")
	viper.AddConfigPath("config")
	viper.AddConfigPath("../config/")
	viper.AddConfigPath("../../config/")
	viper.AddConfigPath("../../../config/")

	if err := viper.ReadInConfig(); err == nil {
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	} else {
		fmt.Println("Cannot read config file config/application.yaml")
		panic(err)
	}

	err := viper.UnmarshalExact(&AppConfig)
	if err != nil {
		panic(fmt.Errorf("Fatal error unable to Unmarshal config file: %s", err))
	}

	return &AppConfig
}

func GetConfig() *Config {
	return &AppConfig
}

func (c *Config) LogLevel() string {
	if "" == c.Log.Level {
		return "info"
	}

	return c.Log.Level
}

func (c *Config) IsProductionEnv() bool {
	return c.Environment == "production"
}

func (c *Config) ListenAddress() string {
	return ":" + strconv.Itoa(c.Server.Port)
}
