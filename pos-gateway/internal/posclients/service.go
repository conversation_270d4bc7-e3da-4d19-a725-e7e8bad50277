package posclients

import (
	"fmt"
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"net/http"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

type POSClientService struct {
	Adapters map[string]adapter.POSAdapter
}

func NewPOSClientService(cfg *config.Config, httpClient *http.Client, utilsClient utils.HTTPClient) POSClientService {
	return POSClientService{Adapters: NewPOSAdapter(cfg, httpClient, utilsClient)}
}

func (s *POSClientService) SendOrder(provider types.Provider, order orders.Order) (types.SuccessResponse, error) {

	adapter, exists := s.Adapters[provider.Name]
	if !exists {
		return types.SuccessResponse{}, fmt.Errorf("adapter not found for provider: %s", provider)
	}
	return adapter.SendOrder(order)
}

func (s *POSClientService) SendRiderDetails(provider types.Provider, rider orders.Rider) (types.SuccessResponse, error) {

	adapter, exists := s.Adapters[provider.Name]
	if !exists {
		return types.SuccessResponse{}, fmt.Errorf("adapter not found for provider: %s", provider)
	}
	return adapter.SendRiderDetails(rider)
}

func (s *POSClientService) SendMenuProcessingRequestStatus(provider types.Provider, status common.MenuProcessingStatus, url string) (types.SuccessResponse, error) {
	adapter, exists := s.Adapters[provider.Name]
	if !exists {
		return types.SuccessResponse{}, fmt.Errorf("adapter not found for provider: %s", provider)
	}
	return adapter.SendMenuProcessingRequestStatus(status, url)
}
