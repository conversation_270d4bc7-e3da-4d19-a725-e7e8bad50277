package adapter

import (
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// POSAdapter defines the interface for POS system integrations
type POSAdapter interface {
	// SendOrder sends order details to POS system
	SendOrder(order orders.Order) (types.SuccessResponse, error)

	// SendRiderDetails sends rider details to POS system
	SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error)

	SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, url string) (types.SuccessResponse, error)
}
