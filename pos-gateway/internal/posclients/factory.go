package posclients

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posclients/freshmenu"
	"github.com/nutanalabs/pos-gateway/internal/posclients/limetray"
	"github.com/nutanalabs/pos-gateway/internal/posclients/restroworks"
	"net/http"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/posclients/petpooja"
	"github.com/nutanalabs/pos-gateway/internal/posclients/urbanpiper"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// POSProvider represents the supported POS providers

// NewPOSAdapter creates a new POS adapter based on the provider
func NewPOSAdapter(cfg *config.Config, httpClient *http.Client,
	utilsClient utils.HTTPClient) map[string]adapter.POSAdapter {
	adapters := make(map[string]adapter.POSAdapter)

	// Create Petpooja adapter
	petpoojaClient := petpooja.NewClient(cfg, httpClient, utilsClient)
	adapters[constants.PetpoojaClientId] = petpooja.NewPetpoojaAdapter(petpoojaClient)

	// Create Urbanpiper adapter
	urbanpiperClient := urbanpiper.NewClient(cfg, httpClient, utilsClient)
	adapters[constants.UrbanpiperClientId] = urbanpiper.NewUrbanpiperAdapter(urbanpiperClient)

	// Create Restroworks adapter
	restroworksClient := restroworks.NewClient(cfg.Restroworks.BaseURL)
	adapters[constants.RestroWorksClientId] = restroworks.NewRestroworksAdapter(restroworksClient)

	freshMenuClient := freshmenu.NewClient(cfg, httpClient, utilsClient)
	adapters[constants.FreshMenuClientId] = freshmenu.NewFreshMenuAdapter(freshMenuClient)

	// Create LimeTray adapter
	limeTrayClient := limetray.NewClient(cfg, httpClient, utilsClient)
	adapters[constants.LimeTrayClientId] = limetray.NewLimeTrayAdapter(limeTrayClient)

	return adapters
}
