package restroworks

import (
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// RestroworksAdapter implements the POSAdapter interface for Restroworks
type RestroworksAdapter struct {
	client *Client
}

// NewRestroworksAdapter creates a new Restroworks adapter instance
func NewRestroworksAdapter(client *Client) adapter.POSAdapter {
	return &RestroworksAdapter{
		client: client,
	}
}

// SendOrder sends order details to Restroworks
func (r *RestroworksAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	transformedOrder := TransformOrder(order)
	_, err := r.client.SendOrder(transformedOrder)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to Restroworks successfully",
	}, nil
}

// SendRiderDetails sends rider details to Restroworks
func (r *RestroworksAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	transformedRider := TransformRider(rider)
	_, err := r.client.SendRiderDetails(transformedRider)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Rider details sent to Restroworks successfully",
	}, nil
}

// SendMenuProcessingRequestStatus sends menu processing status to Restroworks
func (r *RestroworksAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (types.SuccessResponse, error) {
	_, err := r.client.SendMenuProcessingRequestStatus(status, callbackURL)
	return types.SuccessResponse{}, err
}
