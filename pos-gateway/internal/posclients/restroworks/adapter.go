package restroworks

import (
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// RestroworksAdapter implements the POSAdapter interface for Restroworks
type RestroworksAdapter struct {
	client *Client
}

// NewRestroworksAdapter creates a new Restroworks adapter instance
func NewRestroworksAdapter(client *Client) adapter.POSAdapter {
	return &RestroworksAdapter{
		client: client,
	}
}

// SendOrder sends order details to Restroworks
func (r *RestroworksAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	// No transformation needed, pass through unified order structure
	_, err := r.client.SendOrder(order)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to Restroworks successfully",
	}, nil
}

// SendRiderDetails sends rider details to Restroworks
func (r *RestroworksAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	// No transformation needed, pass through unified rider structure
	_, err := r.client.SendRiderDetails(rider)
	return types.SuccessResponse{}, err
}

// SendMenuProcessingRequestStatus sends menu processing status to Restroworks
func (r *RestroworksAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (types.SuccessResponse, error) {
	_, err := r.client.SendMenuProcessingRequestStatus(status, callbackURL)
	return types.SuccessResponse{}, err
}
