package restroworks

// Since Restroworks uses the unified order structure directly,
// we import and re-export the unified order types for consistency

import (
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// Order represents the Restroworks order structure (same as unified)
type Order = orders.Order

// OrderInfo represents the Restroworks order info structure (same as unified)
type OrderInfo = orders.OrderInfo

// Payment represents the Restroworks payment structure (same as unified)
type Payment = orders.Payment

// Customer represents the Restroworks customer structure (same as unified)
type Customer = orders.Customer

// OrderItem represents the Restroworks order item structure (same as unified)
type OrderItem = orders.OrderItem

// Tax represents the Restroworks tax structure (same as unified)
type Tax = orders.Tax

// Charge represents the Restroworks charge structure (same as unified)
type Charge = orders.Charge

// Variant represents the Restroworks variant structure (same as unified)
type Variant = orders.Variant

// AddOn represents the Restroworks addon structure (same as unified)
type AddOn = orders.AddOn

// Rider represents the Restroworks rider structure (same as unified)
type Rider = orders.Rider

// OrderResponse represents the Restroworks API response
type OrderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Success bool   `json:"success,omitempty"`
}

// RiderResponse represents the Restroworks rider API response
type RiderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Success bool   `json:"success,omitempty"`
}
