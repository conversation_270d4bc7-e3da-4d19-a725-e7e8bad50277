package restroworks

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// TransformOrder transforms unified order to Restroworks order format
// Since Restroworks uses the unified structure directly, no transformation is needed
func TransformOrder(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to Restroworks format using struct-based approach
	// Since Restroworks uses unified structure, this will return the order as-is
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.RestroWorksClientId)

	return transformedOrder
}

// TransformRider transforms unified rider to Restroworks rider format
// Since Restroworks uses the unified structure directly, no transformation is needed
func TransformRider(rider orders.Rider) interface{} {
	// For Restroworks, return the unified rider structure as-is
	return rider
}
