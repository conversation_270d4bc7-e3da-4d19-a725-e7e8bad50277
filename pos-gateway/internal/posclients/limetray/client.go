package limetray

import (
	"net/http"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// Client represents the Petpooja API client
type Client struct {
	config      *config.Config
	httpClient  *http.Client
	utilsClient utils.HTTPClient
}

// NewClient creates a new Petpooja API client
func NewClient(cfg *config.Config, httpClient *http.Client,
	utilsClient utils.HTTPClient) *Client {
	return &Client{
		config:      cfg,
		httpClient:  httpClient,
		utilsClient: utilsClient,
	}
}
