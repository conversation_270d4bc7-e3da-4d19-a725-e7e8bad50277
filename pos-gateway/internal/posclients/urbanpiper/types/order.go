package urbanpiper

type SaveOrderRequest struct {
	Meta      OrderMeta    `json:"meta"`
	Payment   PaymentInfo  `json:"payment"`
	Customer  CustomerInfo `json:"customer"`
	Items     []OrderItem  `json:"items"`
	Discounts []Discount   `json:"discounts"`
}

// --- Meta ---
type OrderMeta struct {
	OrderNo            string           `json:"order_no"`
	RestaurantName     string           `json:"restaurant_name"`
	LocationRefID      string           `json:"location_ref_id"`
	CurrentStatus      string           `json:"current_status"`   // Enum: placed, acknowledged, food_ready, dispatched, completed, cancelled
	FulfillmentMode    string           `json:"fulfillment_mode"` // delivery, pickup, delivery_self
	ASAP               bool             `json:"asap"`
	DeliveryDatetime   int64            `json:"delivery_datetime"`
	PickupDatetime     int64            `json:"pickup_datetime"`
	Total              float64          `json:"total"`
	SubTotal           float64          `json:"sub_total"`
	Created            int64            `json:"created"`
	IsEdit             bool             `json:"is_edit"`
	Instructions       string           `json:"instructions"`
	Charges            []ItemCharge     `json:"charges"`
	PrepTimeDetails    *PrepTimeDetails `json:"prep_time_details,omitempty"`
	OrderLevelCharges  float64          `json:"order_level_charges"`
	IsInstantOrder     bool             `json:"is_instant_order"`
	OrderLevelDiscount float64          `json:"order_level_discount"`
	ItemLevelCharges   float64          `json:"item_level_charges"`
	ItemLevelTaxes     float64          `json:"item_level_taxes"`
	ItemLevelDiscount  float64          `json:"item_level_discount"`
	TotalCharges       float64          `json:"total_charges"`
	TotalTaxes         float64          `json:"total_taxes"`
	TotalDiscount      float64          `json:"total_discount"`
	DiscountCode       string           `json:"discount_code"`
}

// --- Discounts ---
type Discount struct {
	Title             string  `json:"title"`
	Code              string  `json:"code"`
	Value             float64 `json:"value"`
	Type              string  `json:"type"` // fixed or pct
	Rate              float64 `json:"rate"`
	MerchantSponsored bool    `json:"merchant_sponsored"`
}

// --- Payment ---
type PaymentInfo struct {
	Mode          string  `json:"mode"`   // cash or online
	Status        string  `json:"status"` // pending or paid
	AmountPaid    float64 `json:"amount_paid"`
	AmountBalance float64 `json:"amount_balance"`
}

// --- Customer ---
type CustomerInfo struct {
	Name        string            `json:"name"`
	PhoneNumber string            `json:"phone_number"`
	Email       string            `json:"email"`
	Address     []CustomerAddress `json:"address"`
}

type CustomerAddress struct {
	Line1        string  `json:"line_1"`
	Line2        string  `json:"line_2"`
	Landmark     string  `json:"landmark"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	City         string  `json:"city"`
	Country      string  `json:"country"`
	Pincode      string  `json:"pincode"`
	Instructions string  `json:"instructions"`
}

// --- Items ---
type OrderItem struct {
	RefID        string        `json:"ref_id"`
	Title        string        `json:"title"`
	PricePerUnit float64       `json:"price_per_unit"`
	Quantity     int           `json:"quantity"`
	Taxes        []ItemTax     `json:"taxes"`
	Charges      []ItemCharge  `json:"charges"`
	Discount     float64       `json:"discount"`
	Total        float64       `json:"total"`
	Subtotal     float64       `json:"subtotal"`
	Variants     []ItemVariant `json:"variants"`
	AddOns       []ItemAddOn   `json:"addons"`
}

type ItemAddOn struct {
	RefID        string  `json:"ref_id"`
	Title        string  `json:"title"`
	PricePerUnit float64 `json:"price_per_unit"`
}

type ItemVariant struct {
	RefID        string        `json:"ref_id"`
	Title        string        `json:"title"`
	PricePerUnit float64       `json:"price_per_unit"`
	Variants     []ItemVariant `json:"variants,omitempty"`
	AddOns       []ItemAddOn   `json:"addons,omitempty"`
}

// --- ItemTax & Charges ---
type ItemTax struct {
	LiabilityOn string  `json:"liability_on"` // aggregator or merchant
	Title       string  `json:"title"`
	Value       float64 `json:"value"`
	Percentage  float64 `json:"percentage"`
}

type ItemCharge struct {
	Title string  `json:"title"`
	Value float64 `json:"value"`
}

// --- Prep Time ---
type PrepTimeDetails struct {
	PredictedPrepTime    int `json:"predicted_prep_time"`
	MaxIncreaseThreshold int `json:"max_increase_threshold"`
	MaxDecreaseThreshold int `json:"max_decrease_threshold"`
}
