package urbanpiper

type MenuPayload struct {
	OrderBillComponents OrderBillComponents `json:"order_bill_components"`
	Menu                Menu                `json:"menu"`
	Timings             []Timing            `json:"timings"`
	Location            Location            `json:"location"`
	BillComponents      BillComponents      `json:"bill_components"`
	CallbackURL         string              `json:"callback_url"`
}

type OrderBillComponents struct {
	Charges []string `json:"charges"`
}

type Menu struct {
	Categories []Category `json:"categories"`
}

type Category struct {
	Subcategories []Subcategory `json:"subcategories"`
	Description   string        `json:"description"`
	Title         string        `json:"title"`
	Translations  []Translation `json:"translations"`
	Items         []Item        `json:"items"`
	Timings       string        `json:"timings"`
	RefID         string        `json:"ref_id"`
	SortOrder     int           `json:"sort_order"`
	ImageURL      string        `json:"image_url"`
}

type Subcategory struct {
	Description  string        `json:"description"`
	Title        string        `json:"title"`
	Translations []Translation `json:"translations"`
	Items        []Item        `json:"items"`
	RefID        string        `json:"ref_id"`
	SortOrder    int           `json:"sort_order"`
	ImageURL     string        `json:"image_url"`
}

type Item struct {
	Description      string             `json:"description"`
	Title            string             `json:"title"`
	Translations     []Translation      `json:"translations"`
	Discounts        []string           `json:"discounts"`
	FulfillmentModes []string           `json:"fulfillment_modes"`
	Tags             []string           `json:"tags"`
	NutritionalInfo  *NutritionalInfo   `json:"nutritional_info,omitempty"`
	Recommended      bool               `json:"recommended"`
	RefID            string             `json:"ref_id"`
	VariantGroups    []VariantGroup     `json:"variant_groups"`
	AddOnGroups      []AddOnGroup       `json:"add_on_groups"`
	CategoryRefID    string             `json:"category_ref_id"`
	CanonicalID      string             `json:"canonical_id"`
	ImageURL         string             `json:"image_url"`
	BillComponents   *ItemBillComponent `json:"bill_components,omitempty"`
	FoodType         int                `json:"food_type"`
	InStock          bool               `json:"in_stock"`
	Price            interface{}        `json:"price"` // can be string or float
	SubCategoryRefID string             `json:"sub_category_ref_id"`
}

type VariantGroup struct {
	Variants     []Variant     `json:"variants"`
	Title        string        `json:"title"`
	Translations []Translation `json:"translations"`
	RefID        string        `json:"ref_id"`
}

type Variant struct {
	Price           float64          `json:"price"`
	Title           string           `json:"title"`
	Translations    []Translation    `json:"translations"`
	InStock         bool             `json:"in_stock"`
	FoodType        int              `json:"food_type"`
	NutritionalInfo *NutritionalInfo `json:"nutritional_info,omitempty"`
	RefID           string           `json:"ref_id"`
	VariantGroups   []VariantGroup   `json:"variant_groups,omitempty"`
	AddOnGroups     []AddOnGroup     `json:"add_on_groups,omitempty"`
}

type AddOnGroup struct {
	MaximumAllowed int           `json:"maximum_allowed"`
	Addons         []AddOn       `json:"addons"`
	Title          string        `json:"title"`
	Translations   []Translation `json:"translations"`
	MinimumNeeded  int           `json:"minimum_needed"`
	RefID          string        `json:"ref_id"`
}

type AddOn struct {
	Price           float64          `json:"price"`
	Title           string           `json:"title"`
	NutritionalInfo *NutritionalInfo `json:"nutritional_info,omitempty"`
	Translations    []Translation    `json:"translations"`
	InStock         bool             `json:"in_stock"`
	FoodType        int              `json:"food_type"`
	RefID           string           `json:"ref_id"`
}

type Translation struct {
	Language    string `json:"language"`
	Title       string `json:"title,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
}

type NutritionalInfo struct {
	Carbohydrate *NutritionData `json:"carbohydrate,omitempty"`
	Fiber        *NutritionData `json:"fiber,omitempty"`
	Fat          *NutritionData `json:"fat,omitempty"`
	Protein      *NutritionData `json:"protein,omitempty"`
	Calorie      *NutritionData `json:"calorie,omitempty"`
	Sugar        *NutritionData `json:"sugar,omitempty"`
	Sodium       *NutritionData `json:"sodium,omitempty"`
	Cholesterol  *NutritionData `json:"cholesterol,omitempty"`
	Salt         *NutritionData `json:"salt,omitempty"`
}

type NutritionData struct {
	Unit  string  `json:"unit"`
	Value float64 `json:"value"`
}

type ItemBillComponent struct {
	Charges []string `json:"charges"`
	Taxes   []string `json:"taxes"`
}

type Timing struct {
	Days  []TimingDay `json:"days"`
	RefID string      `json:"ref_id"`
}

type TimingDay struct {
	Day   string     `json:"day"`
	Slots []TimeSlot `json:"slots"`
}

type TimeSlot struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type Location struct {
	MinDeliveryTime string `json:"min_delivery_time"`
	MinPickupTime   string `json:"min_pickup_time"`
	RefID           string `json:"ref_id"`
}

type BillComponents struct {
	Charges []Charge `json:"charges"`
	Taxes   []Tax    `json:"taxes"`
}

type Charge struct {
	Description      string   `json:"description"`
	Title            string   `json:"title"`
	FulfillmentModes []string `json:"fulfillment_modes"`
	Value            float64  `json:"value"`
	RefID            string   `json:"ref_id"`
	Taxes            []string `json:"taxes"`
	Type             string   `json:"type"`
}

type Tax struct {
	Title       string  `json:"title"`
	Description string  `json:"description"`
	Value       float64 `json:"value"`
	RefID       string  `json:"ref_id"`
}
