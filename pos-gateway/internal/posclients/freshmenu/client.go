package freshmenu

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// Client represents the Freshmenu API client
type Client struct {
	baseURL    string
	httpClient *http.Client
}

// NewClient creates a new Freshmenu client instance
func NewClient(baseURL string) *Client {
	return &Client{
		baseURL:    baseURL,
		httpClient: &http.Client{},
	}
}

// SendOrder sends order details to Freshmenu
func (c *Client) SendOrder(order orders.Order) (*http.Response, error) {
	url := fmt.Sprintf("%s/orders", c.baseURL)
	
	jsonData, err := json.Marshal(order)
	if err != nil {
		return nil, fmt.Errorf("error marshaling order: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}

	return resp, nil
}

// SendRiderDetails sends rider details to Freshmenu
func (c *Client) SendRiderDetails(rider orders.Rider) (*http.Response, error) {
	url := fmt.Sprintf("%s/rider", c.baseURL)
	
	jsonData, err := json.Marshal(rider)
	if err != nil {
		return nil, fmt.Errorf("error marshaling rider: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}

	return resp, nil
}

// SendMenuProcessingRequestStatus sends menu processing status to Freshmenu
func (c *Client) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (*http.Response, error) {
	url := fmt.Sprintf("%s/menu/status", c.baseURL)
	
	statusData := struct {
		Status      common.MenuProcessingStatus `json:"status"`
		CallbackURL string                      `json:"callback_url"`
	}{
		Status:      status,
		CallbackURL: callbackURL,
	}

	jsonData, err := json.Marshal(statusData)
	if err != nil {
		return nil, fmt.Errorf("error marshaling status: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}

	return resp, nil
}
