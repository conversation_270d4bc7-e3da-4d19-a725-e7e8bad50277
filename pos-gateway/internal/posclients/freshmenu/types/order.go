package freshmenu

// Since Freshmenu uses the unified order structure directly,
// we import and re-export the unified order types for consistency

import (
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// Order represents the Freshmenu order structure (same as unified)
type Order = orders.Order

// OrderInfo represents the Freshmenu order info structure (same as unified)
type OrderInfo = orders.OrderInfo

// Payment represents the Freshmenu payment structure (same as unified)
type Payment = orders.Payment

// Customer represents the Freshmenu customer structure (same as unified)
type Customer = orders.Customer

// OrderItem represents the Freshmenu order item structure (same as unified)
type OrderItem = orders.OrderItem

// Tax represents the Freshmenu tax structure (same as unified)
type Tax = orders.Tax

// Charge represents the Freshmenu charge structure (same as unified)
type Charge = orders.Charge

// Variant represents the Freshmenu variant structure (same as unified)
type Variant = orders.Variant

// AddOn represents the Freshmenu addon structure (same as unified)
type AddOn = orders.AddOn

// Rider represents the Freshmenu rider structure (same as unified)
type Rider = orders.Rider

// OrderResponse represents the Freshmenu API response
type OrderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Success bool   `json:"success,omitempty"`
}

// RiderResponse represents the Freshmenu rider API response
type RiderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Success bool   `json:"success,omitempty"`
}
