package freshmenu

import (
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// FreshmenuAdapter implements the POSAdapter interface for Freshmenu
type FreshmenuAdapter struct {
	client *Client
}

// NewFreshmenuAdapter creates a new Freshmenu adapter instance
func NewFreshmenuAdapter(client *Client) adapter.POSAdapter {
	return &FreshmenuAdapter{
		client: client,
	}
}

// SendOrder sends order details to Freshmenu
func (f *FreshmenuAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	transformedOrder := TransformOrder(order)
	_, err := f.client.SendOrder(transformedOrder)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to Freshmen<PERSON> successfully",
	}, nil
}

// SendRiderDetails sends rider details to Freshmenu
func (f *FreshmenuAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	transformedRider := TransformRider(rider)
	_, err := f.client.SendRiderDetails(transformedRider)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Rider details sent to Freshmenu successfully",
	}, nil
}

// SendMenuProcessingRequestStatus sends menu processing status to Freshmenu
func (f *FreshmenuAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (types.SuccessResponse, error) {
	_, err := f.client.SendMenuProcessingRequestStatus(status, callbackURL)
	return types.SuccessResponse{}, err
}
