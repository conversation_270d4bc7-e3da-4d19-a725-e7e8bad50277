package freshmenu

import (
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// FreshmenuAdapter implements the POSAdapter interface for Freshmenu
type FreshmenuAdapter struct {
	client *Client
}

// NewFreshmenuAdapter creates a new Freshmenu adapter instance
func NewFreshmenuAdapter(client *Client) adapter.POSAdapter {
	return &FreshmenuAdapter{
		client: client,
	}
}

// SendOrder sends order details to Freshmenu
func (f *FreshmenuAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	// No transformation needed, pass through unified order structure
	_, err := f.client.SendOrder(order)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to Freshmenu successfully",
	}, nil
}

// SendRiderDetails sends rider details to Freshmenu
func (f *FreshmenuAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	// No transformation needed, pass through unified rider structure
	_, err := f.client.SendRiderDetails(rider)
	return types.SuccessResponse{}, err
}

// SendMenuProcessingRequestStatus sends menu processing status to Freshmenu
func (f *FreshmenuAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (types.SuccessResponse, error) {
	_, err := f.client.SendMenuProcessingRequestStatus(status, callbackURL)
	return types.SuccessResponse{}, err
}
