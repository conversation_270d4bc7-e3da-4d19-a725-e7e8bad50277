package freshmenu

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// TransformOrder transforms unified order to Freshmenu order format
// Since Freshmenu uses the unified structure directly, no transformation is needed
func TransformOrder(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to Freshmenu format using struct-based approach
	// Since Freshmenu uses unified structure, this will return the order as-is
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.FreshMenuClientId)

	return transformedOrder
}

// TransformRider transforms unified rider to Freshmenu rider format
// Since Freshmenu uses the unified structure directly, no transformation is needed
func TransformRider(rider orders.Rider) interface{} {
	// For Freshmenu, return the unified rider structure as-is
	return rider
}
