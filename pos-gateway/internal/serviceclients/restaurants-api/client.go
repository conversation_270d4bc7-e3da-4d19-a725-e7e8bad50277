package restaurants_api

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

type RestaurantAPIClient interface {
	PushMenu(*common.UnifiedMenu) (*map[string]interface{}, error)
	PushInventory(*common.InventoryUpdate) (*map[string]interface{}, error)
	PushStoreStatus(status *common.StoreStatus) (*map[string]interface{}, error)
}

type restaurantAPIClient struct {
	config      *config.Config
	httpClient  *http.Client
	utilsClient utils.HTTPClient
}

func NewRestaurantAPIClient(config *config.Config, httpClient *http.Client, utilsClient utils.HTTPClient) RestaurantAPIClient {
	return &restaurantAPIClient{
		config:      config,
		httpClient:  httpClient,
		utilsClient: utilsClient,
	}
}

func (c *restaurantAPIClient) PushMenu(menu *common.UnifiedMenu) (*map[string]interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		Client:  c.httpClient,
		URL:     "http://localhost:8082/api/v1/menu",
		Body:    menu,
		Timeout: time.Second * 10,
	})
	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = json.Unmarshal(res.Body, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (c *restaurantAPIClient) PushInventory(inventory *common.InventoryUpdate) (*map[string]interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		Client:  c.httpClient,
		URL:     "http://localhost:8082/api/v1/item-status",
		Body:    inventory,
		Timeout: time.Second * 10,
	})
	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = json.Unmarshal(res.Body, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (c *restaurantAPIClient) PushStoreStatus(storeStatus *common.StoreStatus) (*map[string]interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		Client:  c.httpClient,
		URL:     "http://localhost:8082/api/v1/store-status",
		Body:    storeStatus,
		Timeout: time.Second * 10,
	})
	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = json.Unmarshal(res.Body, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}
