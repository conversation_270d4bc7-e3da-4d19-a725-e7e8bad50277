name: Go

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: "1.21"

    - name: Build
      run: go build -v ./...

    - name: Test
      run: go test -v ./...

    - name: go-semantic-release
      uses: go-semantic-release/action@v1.17.0
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        force-bump-patch-version: true
