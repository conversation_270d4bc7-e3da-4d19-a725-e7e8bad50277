{"restaurant": {"id": "53349"}, "categories": [{"id": "139714", "name": "Best in Pizza", "description": "Authentic wood-fired pizzas with fresh ingredients", "imageUrl": "http://image.jpg", "sortOrder": 1, "subcategories": [{"id": "139716", "name": "Vegetarian Pizzas", "description": "Delicious vegetarian pizzas with fresh vegetables", "imageUrl": "http://image.jpg", "sortOrder": 1, "items": [{"id": "701172", "name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomatoes, and basil", "imageUrl": "http://image.jpg", "price": 299.0, "markupPrice": 349.0, "inStock": true, "recommended": true, "isFavorite": true, "foodType": "veg", "tags": ["Classic", "Italian", "Cheese"], "variantGroupIds": ["440072", "440073"], "addonGroupIds": ["440084", "440085"], "nutritionalInfo": {"calorie": {"value": 280, "unit": "kcal"}, "protein": {"value": 12, "unit": "g"}, "carbohydrate": {"value": 35, "unit": "g"}, "fat": {"value": 10, "unit": "g"}, "fiber": {"value": 5, "unit": "g"}, "sodium": {"value": 640, "unit": "mg"}}, "billComponents": {"charges": ["7795"], "taxIds": ["12761", "12762", "12814", "12815"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "139714", "subcategoryId": "139716"}], "timings": "category_timing_001"}, {"id": "139717", "name": "Non-Vegetarian Pizzas", "description": "Mouth-watering non-veg pizzas with premium meats", "imageUrl": "http://image.jpg", "sortOrder": 2, "items": [{"id": "item_chicken_supreme", "name": "Chicken Supreme Pizza", "description": "Loaded with grilled chicken, bell peppers, onions and cheese", "imageUrl": "https://example.com/images/chicken_supreme.jpg", "price": 449.0, "inStock": true, "recommended": false, "isFavorite": false, "foodType": "non veg", "tags": ["Chicken", "Spicy", "Premium"], "variantGroupIds": ["vg_pizza_size"], "addOnGroupIds": ["ag_extra_meat", "ag_beverages"], "nutritionalInfo": {"calorie": {"value": 320, "unit": "kcal", "name": "Calories"}, "protein": {"value": 18, "unit": "g", "name": "<PERSON><PERSON>"}}, "billComponents": {"charges": ["charge_packing", "charge_premium"], "taxes": ["tax_cgst", "tax_sgst"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "cat_pizzas", "subcategoryId": "subcat_nonveg_pizzas", "ignoreOn": {"tax": false, "discounts": false}}, {"id": "item_build_your_own", "name": "Build Your Own Pizza", "description": "Create your perfect pizza with nested customization options", "imageUrl": "https://example.com/images/build_your_own.jpg", "price": 399.0, "inStock": true, "recommended": true, "isFavorite": false, "foodType": "veg", "tags": ["Customizable", "Popular"], "variantGroupIds": ["vg_pizza_size", "vg_specialty_options"], "addOnGroupIds": ["ag_extra_toppings"], "billComponents": {"charges": ["charge_packing"], "taxes": ["tax_cgst", "tax_sgst"]}, "fulfillmentModes": ["delivery"], "sortOrder": 2, "categoryId": "cat_pizzas", "subcategoryId": "subcat_nonveg_pizzas"}]}], "timings": "category_timing_001"}, {"id": "cat_beverages", "name": "Beverages", "description": "Refreshing drinks to complement your meal", "imageUrl": "https://example.com/images/beverages.jpg", "sortOrder": 2, "items": [{"id": "item_coke", "name": "Coca Cola", "description": "Chilled Coca Cola", "imageUrl": "https://example.com/images/coke.jpg", "price": 45.0, "inStock": true, "recommended": false, "isFavorite": false, "foodType": "veg", "tags": ["Cold", "Refreshing"], "variantGroupIds": ["vg_beverage_size"], "billComponents": {"charges": [], "taxes": ["tax_cgst", "tax_sgst"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "cat_beverages"}]}], "timings": [{"id": "restaurant_timing_001", "days": [{"day": "Monday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Tuesday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Wednesday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Thursday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Friday", "slots": [{"startTime": "11:00", "endTime": "23:30"}]}, {"day": "Saturday", "slots": [{"startTime": "11:00", "endTime": "23:30"}]}, {"day": "Sunday", "slots": [{"startTime": "12:00", "endTime": "23:00"}]}]}, {"id": "category_timing_001", "days": [{"day": "Monday", "slots": [{"startTime": "11:00", "endTime": "22:30"}]}]}], "orderBillComponents": {"charges": ["charge_delivery", "charge_service"]}, "addOnGroups": [{"id": "ag_extra_toppings", "name": "Extra Toppings", "minimumNeeded": 0, "maximumAllowed": 5, "addOns": [{"id": "addon_extra_cheese", "name": "Extra Cheese", "price": 50.0, "inStock": true, "foodType": "veg", "nutritionalInfo": {"calorie": {"value": 80, "unit": "kcal", "name": "Calories"}, "totalFat": {"value": 6, "unit": "g", "name": "Total Fat"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://example.com/images/extra_cheese.jpg"}, {"id": "addon_mushrooms", "name": "Mushrooms", "price": 40.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://example.com/images/mushrooms.jpg"}, {"id": "addon_olives", "name": "Black Olives", "price": 35.0, "inStock": false, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 3, "imageUrl": "https://example.com/images/olives.jpg"}], "sortOrder": 1}, {"id": "ag_extra_meat", "name": "Extra Meat", "minimumNeeded": 0, "maximumAllowed": 3, "addOns": [{"id": "addon_chicken", "name": "Extra Chicken", "price": 80.0, "inStock": true, "foodType": "non veg", "nutritionalInfo": {"calorie": {"value": 120, "unit": "kcal", "name": "Calories"}, "protein": {"value": 15, "unit": "g", "name": "<PERSON><PERSON>"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://example.com/images/chicken.jpg"}, {"id": "addon_pepperoni", "name": "<PERSON><PERSON>", "price": 90.0, "inStock": true, "foodType": "non veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://example.com/images/pepperoni.jpg"}], "sortOrder": 2}, {"id": "ag_beverages", "name": "Beverages", "minimumNeeded": 0, "maximumAllowed": 2, "addOns": [{"id": "addon_coke_can", "name": "Coke Can", "price": 45.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://example.com/images/coke_can.jpg"}], "sortOrder": 3}, {"id": "ag_premium_sauces", "name": "Premium Sauces", "minimumNeeded": 1, "maximumAllowed": 3, "addOns": [{"id": "addon_truffle_sauce", "name": "Truffle <PERSON>", "price": 120.0, "inStock": true, "foodType": "veg", "nutritionalInfo": {"calorie": {"value": 60, "unit": "kcal", "name": "Calories"}, "totalFat": {"value": 4, "unit": "g", "name": "Total Fat"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://example.com/images/truffle_sauce.jpg"}, {"id": "addon_pesto_sauce", "name": "<PERSON><PERSON><PERSON>", "price": 80.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://example.com/images/pesto_sauce.jpg"}], "sortOrder": 4}, {"id": "ag_gourmet_cheese", "name": "Gourmet Cheese Selection", "minimumNeeded": 0, "maximumAllowed": 2, "addOns": [{"id": "addon_goat_cheese", "name": "Goat Cheese", "price": 100.0, "inStock": true, "foodType": "veg", "nutritionalInfo": {"calorie": {"value": 90, "unit": "kcal", "name": "Calories"}, "protein": {"value": 6, "unit": "g", "name": "<PERSON><PERSON>"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://example.com/images/goat_cheese.jpg"}], "sortOrder": 5}], "variantGroups": [{"id": "vg_pizza_size", "name": "Pizza Size", "variantIds": ["variant_small", "variant_medium", "variant_large", "variant_xl"], "sortOrder": 1}, {"id": "vg_crust_type", "name": "Crust Type", "variantIds": ["variant_thin", "variant_thick", "variant_cheese_burst"], "sortOrder": 2}, {"id": "vg_beverage_size", "name": "Beverage Size", "variantIds": ["variant_small_drink", "variant_large_drink"], "sortOrder": 1}, {"id": "vg_specialty_options", "name": "Specialty Options", "variantIds": ["variant_classic_base", "variant_gourmet_base", "variant_premium_base"], "sortOrder": 3}], "variants": [{"id": "variant_small", "name": "Small (8 inch)", "price": 0.0, "inStock": true, "foodType": "veg", "addOnGroupIds": ["ag_extra_toppings"], "nutritionalInfo": {"calorie": {"value": 200, "unit": "kcal", "name": "Calories"}, "servingInfo": "1 small pizza (200g)"}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_pizza_size"], "sortOrder": 1}, {"id": "variant_medium", "name": "Medium (10 inch)", "price": 100.0, "markupPrice": 120.0, "inStock": true, "foodType": "veg", "addOnGroupIds": ["ag_extra_toppings", "ag_beverages"], "nutritionalInfo": {"calorie": {"value": 280, "unit": "kcal", "name": "Calories"}, "servingInfo": "1 medium pizza (300g)"}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_pizza_size"], "billComponents": {"charges": ["charge_packing"], "taxes": ["tax_cgst"]}, "sortOrder": 2}, {"id": "variant_large", "name": "Large (12 inch)", "price": 200.0, "inStock": true, "foodType": "veg", "addOnGroupIds": ["ag_extra_toppings", "ag_beverages"], "nutritionalInfo": {"calorie": {"value": 350, "unit": "kcal", "name": "Calories"}, "protein": {"value": 15, "unit": "g", "name": "<PERSON><PERSON>"}, "servingInfo": "1 large pizza (400g)"}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_pizza_size"], "sortOrder": 3}, {"id": "variant_xl", "name": "Extra Large (14 inch)", "price": 300.0, "inStock": false, "foodType": "veg", "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_pizza_size"], "sortOrder": 4}, {"id": "variant_thin", "name": "Thin Crust", "price": 0.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_crust_type"], "sortOrder": 1}, {"id": "variant_thick", "name": "<PERSON><PERSON><PERSON>", "price": 50.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_crust_type"], "sortOrder": 2}, {"id": "variant_cheese_burst", "name": "Cheese Burst Crust", "price": 100.0, "inStock": true, "foodType": "veg", "nutritionalInfo": {"calorie": {"value": 150, "unit": "kcal", "name": "Extra Calories"}, "totalFat": {"value": 12, "unit": "g", "name": "Total Fat"}}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_crust_type"], "sortOrder": 3}, {"id": "variant_small_drink", "name": "Small (300ml)", "price": 0.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_beverage_size"], "sortOrder": 1}, {"id": "variant_large_drink", "name": "Large (500ml)", "price": 20.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_beverage_size"], "sortOrder": 2}, {"id": "variant_classic_base", "name": "Classic Base", "price": 0.0, "inStock": true, "foodType": "veg", "addOnGroupIds": ["ag_extra_toppings"], "nutritionalInfo": {"calorie": {"value": 250, "unit": "kcal", "name": "Calories"}, "servingInfo": "Classic pizza base"}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_specialty_options", "vg_crust_type"], "sortOrder": 1}, {"id": "variant_gourmet_base", "name": "Gourmet Base", "price": 150.0, "markupPrice": 180.0, "inStock": true, "foodType": "veg", "addOnGroupIds": ["ag_extra_toppings", "ag_premium_sauces"], "nutritionalInfo": {"calorie": {"value": 300, "unit": "kcal", "name": "Calories"}, "protein": {"value": 12, "unit": "g", "name": "<PERSON><PERSON>"}, "servingInfo": "Gourmet pizza base with premium ingredients"}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_specialty_options", "vg_crust_type"], "billComponents": {"charges": ["charge_premium"], "taxes": ["tax_cgst", "tax_sgst"]}, "sortOrder": 2}, {"id": "variant_premium_base", "name": "Premium Base", "price": 250.0, "inStock": true, "foodType": "veg", "addOnGroupIds": ["ag_extra_toppings", "ag_premium_sauces", "ag_gourmet_cheese"], "nutritionalInfo": {"calorie": {"value": 380, "unit": "kcal", "name": "Calories"}, "protein": {"value": 18, "unit": "g", "name": "<PERSON><PERSON>"}, "totalFat": {"value": 15, "unit": "g", "name": "Total Fat"}, "servingInfo": "Premium pizza base with artisanal ingredients"}, "fulfillmentModes": ["delivery"], "variantGroupIds": ["vg_specialty_options", "vg_crust_type"], "billComponents": {"charges": ["charge_premium", "charge_packing"], "taxes": ["tax_cgst", "tax_sgst"]}, "sortOrder": 3}], "billComponents": {"charges": [{"id": "charge_packing", "name": "Packing Charge", "description": "Eco-friendly packaging for safe delivery", "value": 15.0, "type": "FIXED", "fulfillmentModes": ["delivery"], "taxes": ["tax_cgst", "tax_sgst"]}, {"id": "charge_premium", "name": "Premium Item Charge", "description": "Additional charge for premium ingredients", "value": 5.0, "type": "PERCENTAGE", "fulfillmentModes": ["delivery"], "taxes": ["tax_cgst"]}, {"id": "charge_delivery", "name": "Delivery Charge", "description": "Home delivery service charge", "value": 30.0, "type": "FIXED", "fulfillmentModes": ["delivery"], "taxes": ["tax_cgst", "tax_sgst"]}, {"id": "charge_service", "name": "Service Charge", "description": "Restaurant service charge", "value": 10.0, "type": "PERCENTAGE", "fulfillmentModes": ["delivery"], "taxes": ["tax_cgst", "tax_sgst"]}], "taxes": [{"id": "tax_cgst", "name": "CGST", "description": "Central Goods and Services Tax - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}, {"id": "tax_sgst", "name": "SGST", "description": "State Goods and Services Tax - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}]}, "callbackUrl": "https://pizza-palace-api.example.com/callback/menu-sync-12345"}