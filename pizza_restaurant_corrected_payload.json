{"restaurant": {"menuSharingCode": "PIZZA_PALACE", "providerId": "53349", "minDeliveryTime": "30", "providerAbbreviation": "RW", "timings": "RW58681", "orderingEnabled": true}, "categories": [{"id": "RW139714", "providerId": "139714", "name": "Pizzas", "description": "Authentic wood-fired pizzas with fresh ingredients", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "sortOrder": 1, "items": [{"id": "RW701172", "providerId": "701172", "name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomatoes, and basil", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 299, "markupPrice": 349, "inStock": true, "recommended": true, "isFavorite": true, "foodType": "veg", "tags": ["Classic", "Italian", "Cheese"], "variantGroupIds": ["RW440072"], "addonGroupIds": ["RW440084"], "nutritionalInfo": {"calorie": {"value": 280, "unit": "kcal"}, "protein": {"value": 12, "unit": "g"}, "carbohydrate": {"value": 35, "unit": "g"}}, "billComponents": {"charges": ["RW7795"], "taxIds": ["RW12761", "RW12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "RW139714"}, {"id": "RW701174", "providerId": "701174", "name": "Pepperoni Pizza", "description": "Classic pepperoni pizza with spicy pepperoni slices", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 399, "inStock": true, "recommended": true, "isFavorite": false, "foodType": "non veg", "tags": ["<PERSON><PERSON>", "Spicy"], "variantGroupIds": ["RW440074"], "addonGroupIds": [], "billComponents": {"charges": ["RW7795"], "taxIds": ["RW12761", "RW12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 2, "categoryId": "RW139714"}]}, {"id": "RW139715", "providerId": "139715", "name": "Beverages", "description": "Refreshing drinks to complement your meal", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "sortOrder": 2, "items": [{"id": "RW701180", "providerId": "701180", "name": "Coca Cola", "description": "Classic refreshing cola drink", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 60, "inStock": true, "recommended": false, "isFavorite": false, "foodType": "veg", "tags": ["Cold", "Refreshing"], "variantGroupIds": ["RW440075"], "addonGroupIds": [], "billComponents": {"charges": ["RW7795"], "taxIds": ["RW12761", "RW12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "RW139715"}]}, {"id": "RW139716", "providerId": "139716", "name": "Specialty Menu", "description": "Our chef's special creations", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "sortOrder": 3, "subcategories": [{"id": "RW139717", "providerId": "139717", "name": "Gourmet Pizzas", "description": "Premium pizzas with exotic ingredients", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "sortOrder": 1, "items": [{"id": "RW701190", "providerId": "701190", "name": "Truffle Supreme", "description": "Luxury pizza with truffle oil and premium cheese", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 899, "inStock": true, "recommended": true, "isFavorite": true, "foodType": "veg", "tags": ["Luxury", "Truffle", "Premium"], "variantGroupIds": ["RW440072"], "addonGroupIds": ["RW440086"], "billComponents": {"charges": ["RW7796"], "taxIds": ["RW12761", "RW12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "RW139716", "subcategoryId": "RW139717"}]}, {"id": "RW139718", "providerId": "139718", "name": "Dessert Pizzas", "description": "Sweet pizzas for dessert lovers", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "sortOrder": 2, "items": [{"id": "RW701195", "providerId": "701195", "name": "Nutella Pizza", "description": "Sweet pizza with Nutella and fresh fruits", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 399, "inStock": true, "recommended": false, "isFavorite": true, "foodType": "veg", "tags": ["Sweet", "Dessert", "<PERSON><PERSON><PERSON>"], "variantGroupIds": ["RW440072"], "addonGroupIds": ["RW440087"], "billComponents": {"charges": ["RW7795"], "taxIds": ["RW12761", "RW12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "RW139716", "subcategoryId": "RW139718"}]}]}], "timings": [{"id": "RW58681", "days": [{"day": "Monday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Tuesday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Wednesday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Thursday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Friday", "slots": [{"startTime": "11:00", "endTime": "23:30"}]}, {"day": "Saturday", "slots": [{"startTime": "11:00", "endTime": "23:30"}]}, {"day": "Sunday", "slots": [{"startTime": "12:00", "endTime": "23:00"}]}]}], "orderBillComponents": {"charges": [{"id": "RW7796", "providerId": "7796", "name": "Delivery Charge", "description": "Home delivery service charge", "value": 30, "type": "FIXED", "fulfillmentModes": null, "taxes": ["RW12763"]}, {"id": "RW7797", "providerId": "7797", "name": "Service Charge", "description": "Restaurant service charge", "value": 10, "type": "PERCENTAGE", "fulfillmentModes": null, "taxes": ["RW12763"]}]}, "addonGroups": [{"id": "RW440084", "providerId": "440084", "name": "Extra Toppings", "minimumNeeded": 0, "maximumAllowed": 4, "addOns": [{"id": "RW1595275", "providerId": "1595275", "name": "Extra Cheese", "price": 50, "inStock": true, "foodType": "veg", "nutritionalInfo": {"calorie": {"value": 80, "unit": "kcal"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "RW1595276", "providerId": "1595276", "name": "Mushrooms", "price": 40, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}], "sortOrder": 1}, {"id": "RW440085", "providerId": "440085", "name": "Meat Toppings", "minimumNeeded": 0, "maximumAllowed": 4, "addOns": [{"id": "RW1595279", "providerId": "1595279", "name": "Extra Chicken", "price": 80, "inStock": true, "foodType": "non veg", "nutritionalInfo": {"protein": {"value": 15, "unit": "g"}, "calorie": {"value": 120, "unit": "kcal"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "RW1595280", "providerId": "1595280", "name": "<PERSON><PERSON>", "price": 90, "inStock": true, "foodType": "non veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}], "sortOrder": 2}, {"id": "RW440086", "providerId": "440086", "name": "Premium Toppings", "minimumNeeded": 0, "maximumAllowed": 2, "addOns": [{"id": "RW1595290", "providerId": "1595290", "name": "Truffle Oil", "price": 200, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}], "sortOrder": 3}, {"id": "RW440087", "providerId": "440087", "name": "Sweet Toppings", "minimumNeeded": 0, "maximumAllowed": 3, "addOns": [{"id": "RW1595295", "providerId": "1595295", "name": "Fresh Strawberries", "price": 80, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}], "sortOrder": 4}], "variantGroups": [{"id": "RW440074", "providerId": "440074", "name": "Specialty Base", "variantIds": ["RW1595524", "RW1595525"], "sortOrder": 0}, {"id": "RW440072", "providerId": "440072", "name": "Size", "variantIds": ["RW1595518", "RW1595519", "RW1595520", "RW1595521"], "sortOrder": 1}, {"id": "RW440073", "providerId": "440073", "name": "Crust", "variantIds": ["RW1595522", "RW1595523"], "sortOrder": 2}, {"id": "RW440075", "providerId": "440075", "name": "Beverage Size", "variantIds": ["RW1595530", "*********"], "sortOrder": 3}], "variants": [{"id": "RW1595518", "providerId": "1595518", "name": "Regular", "price": 0, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": null, "addonGroupIds": [], "sortOrder": 0}, {"id": "RW1595519", "providerId": "1595519", "name": "Medium", "price": 100, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": null, "addonGroupIds": [], "sortOrder": 1}, {"id": "RW1595520", "providerId": "1595520", "name": "Large", "price": 200, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": null, "addonGroupIds": [], "sortOrder": 2}, {"id": "RW1595521", "providerId": "1595521", "name": "Extra Large", "price": 300, "inStock": false, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": null, "addonGroupIds": null, "sortOrder": 3}, {"id": "RW1595522", "providerId": "1595522", "name": "Thin Crust", "price": 0, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": ["RW440072"], "addonGroupIds": [], "sortOrder": 0}, {"id": "RW1595523", "providerId": "1595523", "name": "<PERSON><PERSON><PERSON>", "price": 50, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": ["RW440072"], "addonGroupIds": [], "sortOrder": 1}, {"id": "RW1595524", "providerId": "1595524", "name": "Classic Specialty Base", "price": 0, "inStock": true, "foodType": "veg", "nutritionalInfo": {}, "fulfillmentModes": null, "variantGroupIds": ["RW440073"], "addonGroupIds": ["RW440084"], "sortOrder": 0}, {"id": "RW1595525", "providerId": "1595525", "name": "Premium Specialty Base", "price": 150, "inStock": true, "foodType": "veg", "nutritionalInfo": {}, "fulfillmentModes": null, "variantGroupIds": ["RW440073"], "addonGroupIds": ["RW440084", "RW440085"], "sortOrder": 1}, {"id": "RW1595530", "providerId": "1595530", "name": "Small (300ml)", "price": 0, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": null, "addonGroupIds": [], "sortOrder": 0}, {"id": "*********", "providerId": "1595531", "name": "Large (500ml)", "price": 20, "inStock": true, "foodType": "veg", "fulfillmentModes": null, "variantGroupIds": null, "addonGroupIds": [], "sortOrder": 1}], "billComponents": {"charges": [{"id": "RW7795", "providerId": "7795", "name": "Packing Charge", "description": "Eco-friendly packaging for safe delivery", "value": 15, "type": "FIXED", "fulfillmentModes": ["delivery"], "taxes": ["RW12764"]}, {"id": "RW7796", "providerId": "7796", "name": "Premium Item Charge", "description": "Additional charge for premium ingredients", "value": 5, "type": "PERCENTAGE", "fulfillmentModes": ["delivery"], "taxes": ["RW12761"]}], "taxes": [{"id": "RW12761", "providerId": "12761", "name": "CGST", "description": "Central Goods and Services Tax - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}, {"id": "RW12762", "providerId": "12762", "name": "SGST", "description": "State Goods and Services Tax - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}, {"id": "RW12763", "providerId": "12763", "name": "Delivery Charge Tax", "description": "Tax on delivery charges - 18%", "value": 18, "fulfillmentModes": ["delivery"]}, {"id": "RW12764", "providerId": "12764", "name": "Packing Charge Tax", "description": "Tax on packing charges - 5%", "value": 5, "fulfillmentModes": ["delivery"]}]}, "callbackUrl": "http://localhost:8082/callback/**********"}