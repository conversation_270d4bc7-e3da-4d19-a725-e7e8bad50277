{"restaurant": {"menuSharingCode": "PIZZA_PALACE", "providerId": "53349", "minDeliveryTime": "30", "providerAbbreviation": "RW", "timings": "RW58681", "orderingEnabled": true}, "categories": [{"id": "RW139714", "providerId": "139714", "name": "Pizzas", "description": "Authentic wood-fired pizzas with fresh ingredients", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "sortOrder": 1, "items": [{"id": "701172", "name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomatoes, and basil", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 299.0, "markupPrice": 349.0, "inStock": true, "recommended": true, "isFavorite": true, "foodType": "veg", "tags": ["Classic", "Italian", "Cheese"], "variantGroupIds": ["440072"], "addonGroupIds": ["440084"], "nutritionalInfo": {"calorie": {"value": 280, "unit": "kcal"}, "protein": {"value": 12, "unit": "g"}, "carbohydrate": {"value": 35, "unit": "g"}, "fat": {"value": 10, "unit": "g"}}, "billComponents": {"charges": ["7795"], "taxIds": ["12761", "12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "categoryId": "139714"}, {"id": "701173", "name": "Chicken Supreme Pizza", "description": "Loaded with grilled chicken, bell peppers, onions and cheese", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 449.0, "inStock": true, "recommended": false, "isFavorite": false, "foodType": "non veg", "tags": ["Chicken", "Spicy", "Premium"], "variantGroupIds": ["440072"], "addonGroupIds": ["440085"], "nutritionalInfo": {"calorie": {"value": 320, "unit": "kcal"}, "protein": {"value": 18, "unit": "g"}, "fat": {"value": 15, "unit": "g"}}, "billComponents": {"charges": ["7795"], "taxIds": ["12761", "12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 2, "categoryId": "139714"}, {"id": "701174", "name": "Pepperoni Pizza", "description": "Classic pepperoni pizza with spicy pepperoni slices", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 399.0, "inStock": true, "recommended": true, "isFavorite": false, "foodType": "non veg", "tags": ["<PERSON><PERSON>", "Spicy"], "variantGroupIds": ["440074"], "addonGroupIds": ["440084", "440085"], "billComponents": {"charges": ["7795"], "taxIds": ["12761", "12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 3, "categoryId": "139714"}, {"id": "701175", "name": "Veggie Deluxe Pizza", "description": "Loaded with fresh vegetables and cheese", "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg", "price": 349.0, "inStock": true, "recommended": false, "isFavorite": true, "foodType": "veg", "tags": ["Vegetables", "Healthy"], "variantGroupIds": ["440072"], "addonGroupIds": ["440084"], "nutritionalInfo": {"calorie": {"value": 250, "unit": "kcal"}, "protein": {"value": 10, "unit": "g"}, "fiber": {"value": 8, "unit": "g"}}, "billComponents": {"charges": ["7795"], "taxIds": ["12761", "12762"]}, "fulfillmentModes": ["delivery"], "sortOrder": 4, "categoryId": "139714"}]}], "timings": [{"id": "58681", "days": [{"day": "Monday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Tuesday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Wednesday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Thursday", "slots": [{"startTime": "11:00", "endTime": "23:00"}]}, {"day": "Friday", "slots": [{"startTime": "11:00", "endTime": "23:30"}]}, {"day": "Saturday", "slots": [{"startTime": "11:00", "endTime": "23:30"}]}, {"day": "Sunday", "slots": [{"startTime": "12:00", "endTime": "23:00"}]}]}, {"id": "58719", "days": [{"day": "Wednesday", "slots": [{"startTime": "11:30", "endTime": "16:00"}]}]}], "orderBillComponents": {"charges": [{"id": "7796", "name": "Delivery Charge", "description": "Home delivery service charge", "value": 30, "type": "FIXED", "taxes": ["12763"]}, {"id": "7797", "name": "Service Charge", "description": "Restaurant service charge", "value": 10.0, "type": "PERCENTAGE", "taxes": ["12763"]}]}, "addonGroups": [{"id": "440084", "name": "Extra Toppings", "minimumNeeded": 0, "maximumAllowed": 4, "addOns": [{"id": "1595275", "name": "Extra Cheese", "price": 50.0, "inStock": true, "foodType": "veg", "nutritionalInfo": {"calorie": {"value": 80, "unit": "kcal"}, "fat": {"value": 6, "unit": "g"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "1595276", "name": "Mushrooms", "price": 40.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "1595277", "name": "Black Olives", "price": 35.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 3, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "1595278", "name": "Bell Peppers", "price": 30.0, "inStock": true, "foodType": "veg", "fulfillmentModes": ["delivery"], "sortOrder": 4, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}], "sortOrder": 1}, {"id": "440085", "name": "Meat Toppings", "minimumNeeded": 0, "maximumAllowed": 4, "addOns": [{"id": "1595279", "name": "Extra Chicken", "price": 80.0, "inStock": true, "foodType": "non veg", "nutritionalInfo": {"calorie": {"value": 120, "unit": "kcal"}, "protein": {"value": 15, "unit": "g"}}, "fulfillmentModes": ["delivery"], "sortOrder": 1, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "1595280", "name": "<PERSON><PERSON>", "price": 90.0, "inStock": true, "foodType": "non veg", "fulfillmentModes": ["delivery"], "sortOrder": 2, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "1595281", "name": "Italian Sausage", "price": 85.0, "inStock": true, "foodType": "non veg", "fulfillmentModes": ["delivery"], "sortOrder": 3, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}, {"id": "1595282", "name": "<PERSON>", "price": 95.0, "inStock": false, "foodType": "non veg", "fulfillmentModes": ["delivery"], "sortOrder": 4, "imageUrl": "https://cdn.pixabay.com/photo/2017/12/09/08/18/pizza-3007395_1280.jpg"}], "sortOrder": 2}], "variantGroups": [{"id": "440072", "name": "Size", "variantIds": ["1595518", "1595519", "1595520", "1595521"], "sortOrder": 1}, {"id": "440073", "name": "Crust", "variantIds": ["1595522", "1595523"], "sortOrder": 2}, {"id": "440074", "name": "Specialty Base", "variantIds": ["1595524", "1595525"], "sortOrder": 0}], "variants": [{"id": "1595518", "name": "Regular", "price": 0, "inStock": true, "foodType": "veg", "addonGroupIds": ["440084"], "sortOrder": 0}, {"id": "1595519", "name": "Medium", "price": 100, "inStock": true, "foodType": "veg", "addonGroupIds": ["440084"], "sortOrder": 1}, {"id": "1595520", "name": "Large", "price": 200, "inStock": true, "foodType": "veg", "addonGroupIds": ["440084", "440085"], "sortOrder": 2}, {"id": "1595521", "name": "Extra Large", "price": 300, "inStock": false, "foodType": "veg", "addonGroupIds": null, "sortOrder": 3}, {"id": "1595522", "name": "Thin Crust", "price": 0, "inStock": true, "foodType": "veg", "variantGroupIds": ["440072"], "addonGroupIds": [], "sortOrder": 0}, {"id": "1595523", "name": "<PERSON><PERSON><PERSON>", "price": 50, "inStock": true, "foodType": "veg", "variantGroupIds": ["440072"], "addonGroupIds": [], "sortOrder": 1}, {"id": "1595524", "name": "Classic Specialty Base", "price": 0, "inStock": true, "foodType": "veg", "nutritionalInfo": {}, "variantGroupIds": ["440073"], "addonGroupIds": ["440084"], "sortOrder": 0}, {"id": "1595525", "name": "Premium Specialty Base", "price": 150, "inStock": true, "foodType": "veg", "nutritionalInfo": {}, "variantGroupIds": ["440073"], "addonGroupIds": ["440084", "440085"], "sortOrder": 1}], "billComponents": {"charges": [{"id": "7795", "name": "Packing Charge", "description": "Eco-friendly packaging for safe delivery", "value": 15.0, "type": "FIXED", "fulfillmentModes": ["delivery"], "taxes": ["12764"]}, {"id": "7796", "name": "Premium Item Charge", "description": "Additional charge for premium ingredients", "value": 5.0, "type": "PERCENTAGE", "fulfillmentModes": ["delivery"], "taxes": ["12761"]}, {"id": "7797", "name": "Handling Charge", "description": "Restaurant handling charge", "value": 30.0, "type": "FIXED", "fulfillmentModes": ["delivery"], "taxes": ["12763"]}], "taxes": [{"id": "12761", "name": "CGST", "description": "Central Goods and Services Tax - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}, {"id": "12762", "name": "SGST", "description": "State Goods and Services Tax - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}, {"id": "12763", "name": "Delivery Charge Tax", "description": "Tax on delivery charges - 18%", "value": 18.0, "fulfillmentModes": ["delivery"]}, {"id": "12764", "name": "Packing Charge Tax", "description": "Tax on packing charges - 5%", "value": 5.0, "fulfillmentModes": ["delivery"]}, {"id": "12814", "name": "SGST Additional", "description": "Additional SGST - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}, {"id": "12815", "name": "CGST Additional", "description": "Additional CGST - 2.5%", "value": 2.5, "fulfillmentModes": ["delivery"]}]}, "callbackUrl": "https://pizza-palace-api.example.com/callback/menu-sync-12345"}