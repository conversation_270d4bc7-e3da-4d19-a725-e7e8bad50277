package handler

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/validation"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
	"strings"
)

func (h *Handler) handlerCallbackProcessing(clientID string, url string, processingStatus bool, validationErrors *[]validation.ValidationError) *types.StatusError {
	requestBody := common.MenuProcessingStatus{
		Success:   processingStatus,
		Data:      *validationErrors,
		RequestID: extractIDFromURL(url),
	}
	_, err := h.posClientService.Adapters[clientID].SendMenuProcessingRequestStatus(requestBody, url)

	if err != nil {
		return &types.StatusError{
			DisplayMessage: "Failed to send menu processing status",
			Message:        fmt.Sprintf("Failed to send menu processing status to POS provider. err: %v ", utils.ConvertInterfaceToJSON(err)),
			Code:           "error_sending_menu_processing_status",
			Data: &map[string]interface{}{
				"payload": utils.ConvertInterfaceToJSON(requestBody),
			},
		}
	}

	logger.Debug(logger.Format{
		Message: "Menu processing status sent successfully",
		Data: map[string]string{
			"clientID": clientID,
			"url":      url,
			"status":   "success",
			"payload":  utils.ConvertInterfaceToJSON(requestBody),
		},
	})
	return nil

}

func getXConsumerUserName(ctx *gin.Context) string {
	return ctx.GetHeader("x-consumer-username")
}

func extractIDFromURL(url string) string {
	parts := strings.Split(strings.TrimSuffix(url, "/"), "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return ""
}
