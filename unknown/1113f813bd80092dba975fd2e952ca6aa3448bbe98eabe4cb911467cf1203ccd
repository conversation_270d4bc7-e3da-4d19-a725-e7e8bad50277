package kafka

import "github.com/stretchr/testify/mock"

type MockKafka struct {
	mock.Mock
}

func (k *MockKafka) SendMessage(topic string, message []byte) error {
	ret := k.Called(topic, message)
	return ret.Error(0)
}

func (k *<PERSON>ckKafka) SendKeyedMessage(topic string, key string, message []byte) error {
	ret := k.Called(topic, key, message)
	return ret.Error(0)
}

func (k *MockKafka) CheckBrokers() error {
	ret := k.Called()
	return ret.Error(0)
}
