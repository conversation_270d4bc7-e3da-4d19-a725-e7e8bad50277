package mongo

import (
	"context"
	"fmt"
	"github.com/nutanalabs/pos-gateway/internal/config"
	logger "github.com/roppenlabs/rapido-logger-go"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

func NewMongoClient(config *config.Config) *mongo.Client {
	logger.Info(logger.Format{
		Message: "Initializing mongo connection",
	})

	clientOptions := options.Client().ApplyURI(config.MongoURI()).SetMinPoolSize(30)
	client, err := mongo.NewClient(clientOptions)
	if err != nil {
		logger.Error(logger.Format{
			Message: fmt.Sprintf("Error creating mongo client: %v", err),
		})
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	connectErr := client.Connect(ctx)
	if connectErr != nil {
		logger.Error(logger.Format{
			Message: fmt.Sprintf("Error connecting to mongo: %v", err),
		})
	}

	logger.Debug(logger.Format{
		Message: "Connected to mongo",
		Data: map[string]string{
			"mongoURI": config.MongoURI(),
		},
	})
	return client
}
