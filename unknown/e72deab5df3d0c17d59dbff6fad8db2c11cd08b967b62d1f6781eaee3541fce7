package orders

// Order represents the complete order structure
type Order struct {
	OrderInfo OrderInfo   `json:"orderInfo"`
	Payment   Payment     `json:"payment"`
	Customer  Customer    `json:"customer"`
	Items     []OrderItem `json:"item"`
}

// OrderInfo contains the key details about the order
type OrderInfo struct {
	OrderID            string  `json:"orderId"`
	RestID             string  `json:"restId"`
	Instruction        string  `json:"instruction,omitempty"`
	Status             string  `json:"status"`
	SubTotal           float64 `json:"subTotal"`
	TotalPackingCharge float64 `json:"totalPackingCharge"`
	TotalTaxes         float64 `json:"totalTaxes"`
	CreatedAt          int     `json:"createdAt"`
	Total              float64 `json:"total"`
	DeliveryMode       string  `json:"deliveryMode,omitempty"`
}

// Payment contains payment details
type Payment struct {
	Mode       string  `json:"mode"`
	Status     string  `json:"status"`
	AmountPaid float64 `json:"amountPaid"`
}

// Customer contains customer details
type Customer struct {
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
}

// OrderItem represents an individual item in the order
type OrderItem struct {
	ItemID      string    `json:"itemId"`
	Name        string    `json:"name"`
	Quantity    int       `json:"quantity"`
	UnitPrice   float64   `json:"unitPrice"`
	Taxes       []Tax     `json:"taxes"`
	Charges     []Charge  `json:"charges"`
	Instruction string    `json:"instruction,omitempty"`
	Variants    []Variant `json:"variants,omitempty"`
	AddOns      []AddOn   `json:"addOns,omitempty"`
}

// Variant represents item variant details
type Variant struct {
	ID        string    `json:"id"`
	Name      string    `json:"name,omitempty"`
	UnitPrice float64   `json:"unitPrice"`
	Variants  []Variant `json:"variants,omitempty"`
	AddOns    []AddOn   `json:"addOns,omitempty"`
}

// AddOn represents additional items that can be added to an order item
type AddOn struct {
	ID        string  `json:"id"`
	Name      string  `json:"name"`
	UnitPrice float64 `json:"unitPrice"`
}

// Tax represents tax details
type Tax struct {
	Title       string  `json:"title"`
	Value       float64 `json:"value"`
	Percentage  float64 `json:"percentage"`
	LiabilityOn string  `json:"liability_on"`
}

// Charge represents additional charges
type Charge struct {
	Title       string  `json:"title"`
	Value       float64 `json:"value"`
	LiabilityOn string  `json:"liability_on"`
}

// OrderResponse represents the API response
type OrderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// Rider represents rider details (kept for POS client compatibility)
type Rider struct {
	RiderID   string `json:"rider_id,omitempty"`
	RiderName string `json:"rider_name,omitempty"`
}
