package rlogger

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/suite"
)

type LoggerTestSuite struct {
	suite.Suite
}

func (k *LoggerTestSuite) SetupTest() {

}
func (k *LoggerTestSuite) TestShouldPrintLogs() {
	Init("debug")

	msg := Format{
		RequestID: "RequestId",
		BranchID:  "BranchId",
		ClientID:  "ClientId",
		Endpoint:  "",
		Message:   "Message",
		Data: map[string]string{
			"one": "one",
			"two": "two",
		},
	}

	msg.Message = "I am debug"
	fmt.Println("---Debug---")
	Debug(msg)
	Info(msg)
	Warn(msg)
	Error(msg)

	msg.Message = "I am info"
	Init("info")
	fmt.Println("---Info---")
	Debug(msg)
	Info(msg)
	Warn(msg)
	Error(msg)

	msg.Message = "I am warn"
	Init("warn")
	fmt.Println("---Warn---")
	Debug(msg)
	Info(msg)
	Warn(msg)
	Error(msg)

	msg.Message = "I am error"
	Init("error")
	fmt.Println("---Error---")
	Debug(msg)
	Info(msg)
	Warn(msg)
	Error(msg)
}

func (k *LoggerTestSuite) TestShouldInitOnce() {
	Init("debug")
	Init("info")
	Init("error")
	Debug(Format{})
}
func TestLoggerTestSuite(t *testing.T) {
	suite.Run(t, new(LoggerTestSuite))
}
