// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/dataclients/kafka"
	"github.com/nutanalabs/pos-gateway/internal/dataclients/mongo"
	"github.com/nutanalabs/pos-gateway/internal/health"
	"github.com/nutanalabs/pos-gateway/internal/posclients"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/repository"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/service"
	"github.com/nutanalabs/pos-gateway/internal/server"
	"github.com/nutanalabs/pos-gateway/internal/serviceclients/restaurants-api"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// Injectors from di.go:

func InitDependencies() (ServerDependencies, error) {
	configConfig := config.GetConfig()
	serverServer := server.NewServer(configConfig)
	healthHandler := health.NewHandler()
	client := server.NewHTTPClient()
	httpClient := utils.GetHTTPClient()
	posClientService := posclients.NewPOSClientService(configConfig, client, httpClient)
	producerInterface, err := kafka.NewKafkaProducerInstance(configConfig)
	if err != nil {
		return ServerDependencies{}, err
	}
	utilsUtils := utils.NewUtils()
	kafkaRepoInterface := repository.NewKafkaRepo(producerInterface, utilsUtils)
	restaurantAPIClient := restaurants_api.NewRestaurantAPIClient(configConfig, client, httpClient)
	mongoClient := mongo.NewMongoClient(configConfig)
	mongoRepository := repository.NewMongoRepository(mongoClient, configConfig)
	serviceService := service.NewService(configConfig, posClientService, kafkaRepoInterface, restaurantAPIClient, mongoRepository)
	transformersTransformers := transformers.NewTransformers(utilsUtils)
	handlerHandler := handler.NewHandler(serviceService, utilsUtils, transformersTransformers, posClientService)
	handlers := server.Handlers{
		HealthHandler:    healthHandler,
		MyPackageHandler: handlerHandler,
	}
	serverDependencies := ServerDependencies{
		config:   configConfig,
		server:   serverServer,
		handlers: handlers,
	}
	return serverDependencies, nil
}

// di.go:

type ServerDependencies struct {
	config   *config.Config
	server   *server.Server
	handlers server.Handlers
}
