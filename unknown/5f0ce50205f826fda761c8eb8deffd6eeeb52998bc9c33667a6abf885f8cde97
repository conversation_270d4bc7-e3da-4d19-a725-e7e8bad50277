package petpooja

// SaveOrderRequest represents the Petpooja order request structure
type SaveOrderRequest struct {
	AppKey             string    `json:"app_key"`
	AppSecret          string    `json:"app_secret"`
	AccessToken        string    `json:"access_token"`
	ResName            string    `json:"res_name"`
	Address            string    `json:"address"`
	ContactInformation string    `json:"Contact_information"`
	RestID             string    `json:"restID"`
	OrderInfo          OrderInfo `json:"OrderInfo"`
	UDID               string    `json:"udid"`
	DeviceType         string    `json:"device_type"`
}

// OrderInfo represents the main order information container according to Petpooja API
type OrderInfo struct {
	Customer  Customer    `json:"Customer"`
	Order     Order       `json:"Order"`
	OrderItem []OrderItem `json:"OrderItem"`
	Tax       []Tax       `json:"Tax"`
	Discount  []Discount  `json:"Discount"`
}

// Customer represents customer information
type Customer struct {
	Email     string `json:"email"`
	Name      string `json:"name"`
	Address   string `json:"address"`
	Phone     string `json:"phone"`
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

// Order represents order information according to Petpooja API
type Order struct {
	OrderID         string      `json:"orderID"`
	PreorderDate    string      `json:"preorder_date"`
	PreorderTime    string      `json:"preorder_time"`
	ServiceCharge   string      `json:"service_charge"`
	SCTaxAmount     string      `json:"sc_tax_amount"`
	DeliveryCharges string      `json:"delivery_charges"`
	DCTaxAmount     string      `json:"dc_tax_amount"`
	DCGSTDetails    []GSTDetail `json:"dc_gst_details,omitempty"`
	PackingCharges  string      `json:"packing_charges"`
	PCTaxAmount     string      `json:"pc_tax_amount"`
	PCGSTDetails    []GSTDetail `json:"pc_gst_details,omitempty"`
	OrderType       string      `json:"order_type"`
	ONDCBap         string      `json:"ondc_bap,omitempty"`
	AdvancedOrder   string      `json:"advanced_order"`
	UrgentOrder     bool        `json:"urgent_order"`
	UrgentTime      int         `json:"urgent_time"`
	PaymentType     string      `json:"payment_type"`
	TableNo         string      `json:"table_no"`
	NoOfPersons     string      `json:"no_of_persons"`
	DiscountTotal   string      `json:"discount_total"`
	Discount        string      `json:"discount"`
	DiscountType    string      `json:"discount_type"`
	Total           string      `json:"total"`
	TaxTotal        string      `json:"tax_total"`
	Description     string      `json:"description"`
	CreatedOn       string      `json:"created_on"`
	EnableDelivery  int         `json:"enable_delivery"`
	MinPrepTime     int         `json:"min_prep_time,omitempty"`
	CallbackURL     string      `json:"callback_url"`
	CollectCash     string      `json:"collect_cash,omitempty"`
	OTP             string      `json:"otp,omitempty"`
}

// GSTDetail represents GST details for charges
type GSTDetail struct {
	GSTLiable string `json:"gst_liable"`
	Amount    string `json:"amount"`
}

// OrderItem represents individual order item according to Petpooja API
type OrderItem struct {
	ID            string      `json:"id"`
	Name          string      `json:"name"`
	GSTLiability  string      `json:"gst_liability,omitempty"`
	ItemTax       []ItemTax   `json:"item_tax"`
	ItemDiscount  string      `json:"item_discount"`
	Price         string      `json:"price"`
	FinalPrice    string      `json:"final_price"`
	Quantity      string      `json:"quantity"`
	Description   string      `json:"description"`
	VariationName string      `json:"variation_name,omitempty"`
	VariationID   string      `json:"variation_id,omitempty"`
	AddonItem     []AddonItem `json:"AddonItem,omitempty"`
}

// ItemTax represents tax on individual items
type ItemTax struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Amount string `json:"amount"`
}

// AddonItem represents individual addon item according to Petpooja API
type AddonItem struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	GroupName string `json:"group_name"`
	Price     string `json:"price"`
	GroupID   string `json:"group_id"` // Fixed: should be string, not int
	Quantity  string `json:"quantity"`
}

// Tax represents individual tax details according to Petpooja API
type Tax struct {
	ID                  string `json:"id"`
	Title               string `json:"title"`
	Type                string `json:"type"`
	Price               string `json:"price"`
	Tax                 string `json:"tax"`
	RestaurantLiableAmt string `json:"restaurant_liable_amt,omitempty"`
}

// Discount represents individual discount details according to Petpooja API
type Discount struct {
	ID    string `json:"id,omitempty"`
	Title string `json:"title"`
	Type  string `json:"type"`
	Price string `json:"price"`
}
