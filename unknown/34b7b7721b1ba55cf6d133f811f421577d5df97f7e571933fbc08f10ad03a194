package transformers

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
)

func (t *transformersImpl) TransformIntoUnifiedMenu(menu *map[string]interface{}, clientId string) *common.UnifiedMenu {
	var unifiedMenu common.UnifiedMenu
	err := utils.UnmarshalJSONToInterface(menu, &unifiedMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling unifiedMenu menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}

	// Set restaurant IDs
	if unifiedMenu.Restaurant != nil {
		unifiedMenu.Restaurant.ProviderId = unifiedMenu.Restaurant.Id
		unifiedMenu.Restaurant.Id = nil
		unifiedMenu.Restaurant.ProviderAbbreviation = constants.ProviderAbbreviationMap[clientId]
	}

	// Process categories and their nested resources
	if unifiedMenu.Categories != nil {
		for i := range unifiedMenu.Categories {
			processCategory(&unifiedMenu.Categories[i], clientId)
		}
	}

	// Process variants
	if unifiedMenu.Variants != nil {
		for i := range unifiedMenu.Variants {
			processVariant(&unifiedMenu.Variants[i], clientId)
		}
	}

	// Process variant groups
	if unifiedMenu.VariantGroups != nil {
		for i := range unifiedMenu.VariantGroups {
			processVariantGroup(&unifiedMenu.VariantGroups[i], clientId)
		}
	}

	// Process addon groups
	if unifiedMenu.AddOnGroups != nil {
		for i := range unifiedMenu.AddOnGroups {
			processAddonGroup(&unifiedMenu.AddOnGroups[i], clientId)
		}
	}

	// Process bill components at menu level
	if unifiedMenu.BillComponents != nil {
		processBillComponents(unifiedMenu.BillComponents, clientId)
	}

	// Process order bill components
	if unifiedMenu.OrderBillComponents != nil {
		processOrderBillComponents(unifiedMenu.OrderBillComponents, clientId)
	}

	// process timings
	if unifiedMenu.Timings != nil {
		for i := range unifiedMenu.Timings {
			providerId := constants.ProviderAbbreviationMap[clientId] + *unifiedMenu.Timings[i].ID
			unifiedMenu.Timings[i].ID = &providerId
		}
	}

	return &unifiedMenu
}

// processCategory recursively processes a category and its resources
func processCategory(category *common.Category, clientId string) {
	// Set category IDs
	if category.ID != nil && *category.ID != "" {
		category.ProviderId = category.ID
		category.ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *category.ProviderId)
	}

	// Process timings
	if category.Timings != nil {
		category.Timings = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *category.Timings)
	}

	// Process items in the category
	for i := range category.Items {
		processItem(&category.Items[i], clientId)
	}

	// Recursively process subcategories
	if category.Subcategories != nil {
		for i := range category.Subcategories {
			processCategory(&category.Subcategories[i], clientId)
		}
	}
}

// processItem processes an item and its resources
func processItem(item *common.Item, clientId string) {
	// Set item IDs
	if item.ID != nil {
		item.ProviderId = item.ID
		item.ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *item.ProviderId)
	}

	// Process category ID
	if item.CategoryID != nil && *item.CategoryID != "" {
		item.CategoryID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *item.CategoryID)
	}

	// Process subcategory ID
	if item.SubcategoryID != nil && *item.SubcategoryID != "" {
		item.SubcategoryID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *item.SubcategoryID)
	}

	// Process variant group IDs
	if item.VariantGroupIDs != nil {
		for i := range item.VariantGroupIDs {
			item.VariantGroupIDs[i] = constants.ProviderAbbreviationMap[clientId] + item.VariantGroupIDs[i]
		}
	}

	// Process addon group IDs
	if item.AddOnGroupIDs != nil {
		for i := range item.AddOnGroupIDs {
			item.AddOnGroupIDs[i] = constants.ProviderAbbreviationMap[clientId] + item.AddOnGroupIDs[i]
		}
	}

	// Process bill components if present
	if item.BillComponents != nil {
		// Process tax IDs
		if item.BillComponents.TaxIDs != nil {
			for i := range item.BillComponents.TaxIDs {
				item.BillComponents.TaxIDs[i] = constants.ProviderAbbreviationMap[clientId] + item.BillComponents.TaxIDs[i]
			}
		}

		// Process charge IDs
		if item.BillComponents.Charges != nil {
			for i := range item.BillComponents.Charges {
				item.BillComponents.Charges[i] = constants.ProviderAbbreviationMap[clientId] + item.BillComponents.Charges[i]
			}
		}
	}
}

// processVariant processes a variant
func processVariant(variant *common.Variant, clientId string) {
	// Set variant IDs
	if variant.ID != nil {
		variant.ProviderId = variant.ID
		variant.ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *variant.ProviderId)
	}

	// Process addon group IDs
	if variant.AddOnGroupIDs != nil {
		for i := range variant.AddOnGroupIDs {
			variant.AddOnGroupIDs[i] = constants.ProviderAbbreviationMap[clientId] + variant.AddOnGroupIDs[i]
		}
	}

	// Process variant group IDs
	if variant.VariantGroupIDs != nil {
		for i := range variant.VariantGroupIDs {
			variant.VariantGroupIDs[i] = constants.ProviderAbbreviationMap[clientId] + variant.VariantGroupIDs[i]
		}
	}

	// Process bill components if present
	if variant.BillComponents != nil {
		// Process tax IDs
		if variant.BillComponents.TaxIDs != nil {
			for i := range variant.BillComponents.TaxIDs {
				variant.BillComponents.TaxIDs[i] = constants.ProviderAbbreviationMap[clientId] + variant.BillComponents.TaxIDs[i]
			}
		}

		// Process charge IDs
		if variant.BillComponents.Charges != nil {
			for i := range variant.BillComponents.Charges {
				variant.BillComponents.Charges[i] = constants.ProviderAbbreviationMap[clientId] + variant.BillComponents.Charges[i]
			}
		}
	}
}

// processVariantGroup processes a variant group and its variant IDs
func processVariantGroup(group *common.VariantGroup, clientId string) {
	// Set variant group IDs
	if group.ID != nil {
		group.ProviderId = group.ID
		group.ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *group.ProviderId)
	}

	// Process variant IDs
	if group.VariantIDs != nil {
		for i := range group.VariantIDs {
			group.VariantIDs[i] = constants.ProviderAbbreviationMap[clientId] + group.VariantIDs[i]
		}
	}
}

// processAddonGroup processes an addon group and its addons
func processAddonGroup(group *common.AddOnGroup, clientId string) {
	// Set addon group IDs
	if group.ID != nil {
		group.ProviderId = group.ID
		group.ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *group.ProviderId)
	}

	// Process addons in the group
	for i := range group.AddOns {
		processAddon(&group.AddOns[i], clientId)
	}
}

// processAddon processes an addon
func processAddon(addon *common.AddOn, clientId string) {
	// Set addon IDs
	if addon.ID != nil {
		addon.ProviderId = addon.ID
		addon.ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *addon.ProviderId)
	}
}

// processBillComponents processes menu-level bill components (taxes and charges)
func processBillComponents(components *common.BillComponents, clientId string) {
	// Process taxes
	if components.Taxes != nil {
		for i := range components.Taxes {
			if components.Taxes[i].ID != nil {
				components.Taxes[i].ProviderId = components.Taxes[i].ID
				components.Taxes[i].ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *components.Taxes[i].ProviderId)
			}
		}
	}

	// Process charges
	if components.Charges != nil {
		for i := range components.Charges {
			if components.Charges[i].ID != nil {
				components.Charges[i].ProviderId = components.Charges[i].ID
				components.Charges[i].ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *components.Charges[i].ProviderId)
			}

			// Process tax IDs in charges
			if components.Charges[i].Taxes != nil {
				for j := range components.Charges[i].Taxes {
					components.Charges[i].Taxes[j] = constants.ProviderAbbreviationMap[clientId] + components.Charges[i].Taxes[j]
				}
			}
		}
	}
}

// processOrderBillComponents processes order-level bill components (charges and their taxes)
func processOrderBillComponents(components *common.OrderBillComponents, clientId string) {
	// Process charges
	if components.Charges != nil {
		for i := range components.Charges {
			if components.Charges[i].ID != nil {
				components.Charges[i].ProviderId = components.Charges[i].ID
				components.Charges[i].ID = utils.StringPtr(constants.ProviderAbbreviationMap[clientId] + *components.Charges[i].ProviderId)
			}

			// Process tax IDs in charges
			if components.Charges[i].Taxes != nil {
				for j := range components.Charges[i].Taxes {
					components.Charges[i].Taxes[j] = constants.ProviderAbbreviationMap[clientId] + components.Charges[i].Taxes[j]
				}
			}
		}
	}
}
