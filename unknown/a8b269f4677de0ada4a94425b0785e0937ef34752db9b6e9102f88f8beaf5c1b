package menu

import (
	"fmt"
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/validation"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

const (
	CategoryError     string = "category"
	SubcategoryError  string = "subcategory"
	ItemError         string = "item"
	VariantGroupError string = "variantGroup"
	AddonGroupError   string = "addonGroup"
	VariantError      string = "variant"
	AddonError        string = "addon"
	TaxError          string = "tax"
	ChargeError       string = "charge"
)

// ValidateMenu performs all menu validations and returns any validation errors
func ValidateMenu(menu *common.UnifiedMenu) *[]validation.ValidationError {
	var errors []validation.ValidationError

	// Validate categories and items
	if menu.Categories != nil {
		for _, category := range menu.Categories {
			validateCategory(&category, &errors)
		}
	}

	// Validate variants at menu level
	if menu.Variants != nil {
		validateVariants(menu.Variants, &errors)
	}

	// Validate addon groups at menu level
	if menu.AddOnGroups != nil {
		validateAddonGroups(menu.AddOnGroups, &errors)
	}

	// Validate bill components (taxes and charges) at menu level
	if menu.BillComponents != nil {
		validateBillComponents(menu.BillComponents, &errors)
	}

	return &errors
}

// validateCategory validates a category and its subcategories
func validateCategory(category *common.Category, errors *[]validation.ValidationError) {
	// Check if category has both items and subcategories
	if len(category.Items) > 0 && len(category.Subcategories) > 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       CategoryError,
			ResourceID: *category.ProviderId,
			Reason:     "items and subcategory should not coexist under a category",
		})
	}

	// Validate items in current category
	for _, item := range category.Items {
		// Check item price
		if item.Price != nil && *item.Price < 0 {
			*errors = append(*errors, validation.ValidationError{
				Type:       ItemError,
				ResourceID: *item.ProviderId,
				Reason:     "item price cannot be negative",
			})
		}

		if item.FoodType != nil && !utils.SliceContains(constants.AllowedFoodTypes, *item.FoodType) {
			*errors = append(*errors, validation.ValidationError{
				Type:       ItemError,
				ResourceID: *item.ProviderId,
				Reason:     fmt.Sprintf("invalid food type. Allowed type: %v", constants.AllowedFoodTypes),
			})
		}
	}

	// Recursively validate subcategories
	if category.Subcategories != nil {
		for _, subcategory := range category.Subcategories {
			validateCategory(&subcategory, errors)
		}
	}
}

// validateVariants validates all variants at menu level
func validateVariants(variants []common.Variant, errors *[]validation.ValidationError) {
	for _, variant := range variants {
		// Check variant price
		if variant.Price != nil && *variant.Price < 0 {
			*errors = append(*errors, validation.ValidationError{
				Type:       VariantError,
				ResourceID: *variant.ProviderId,
				Reason:     "variant price cannot be negative",
			})
		}

		// check if food type is valid
		if variant.FoodType != nil && !utils.SliceContains(constants.AllowedFoodTypes, *variant.FoodType) {
			*errors = append(*errors, validation.ValidationError{
				Type:       VariantError,
				ResourceID: *variant.ProviderId,
				Reason:     fmt.Sprintf("invalid food type. Allowed type: %v", constants.AllowedFoodTypes),
			})
		}
	}
}

// validateAddonGroups validates all addon groups and their addons at menu level
func validateAddonGroups(addonGroups []common.AddOnGroup, errors *[]validation.ValidationError) {
	for _, group := range addonGroups {
		// Validate min/max allowed values
		if group.MinimumNeeded != nil && group.MaximumAllowed != nil {
			// Check if max is less than min
			if *group.MaximumAllowed < *group.MinimumNeeded {
				*errors = append(*errors, validation.ValidationError{
					Type:       AddonGroupError,
					ResourceID: *group.ProviderId,
					Reason:     "max allowed cannot be less than min allowed",
				})
			}
			// Check if max is negative
			if *group.MaximumAllowed < 0 {
				*errors = append(*errors, validation.ValidationError{
					Type:       AddonGroupError,
					ResourceID: *group.ProviderId,
					Reason:     "max allowed cannot be negative",
				})
			}
			// Check if max is zero
			if *group.MaximumAllowed == 0 {
				*errors = append(*errors, validation.ValidationError{
					Type:       AddonGroupError,
					ResourceID: *group.ProviderId,
					Reason:     "max allowed cannot be zero",
				})
			}
		}

		// Check if number of addons is greater than or equal to min allowed
		if group.MinimumNeeded != nil && len(group.AddOns) < *group.MinimumNeeded {
			*errors = append(*errors, validation.ValidationError{
				Type:       AddonGroupError,
				ResourceID: *group.ProviderId,
				Reason:     "number of addons cannot be less than min allowed",
			})
		}

		// Validate addons in the group
		for _, addon := range group.AddOns {
			if addon.Price != nil && *addon.Price < 0 {
				*errors = append(*errors, validation.ValidationError{
					Type:       AddonError,
					ResourceID: *addon.ProviderId,
					Reason:     "addon price cannot be negative",
				})
			}
			if addon.FoodType != nil && !utils.SliceContains(constants.AllowedFoodTypes, *addon.FoodType) {
				*errors = append(*errors, validation.ValidationError{
					Type:       AddonError,
					ResourceID: *addon.ProviderId,
					Reason:     fmt.Sprintf("invalid food type. Allowed type: %v", constants.AllowedFoodTypes),
				})
			}
		}
	}
}

// validateBillComponents validates taxes and charges at menu level
func validateBillComponents(components *common.BillComponents, errors *[]validation.ValidationError) {
	// Validate taxes
	if components.Taxes != nil {
		for _, tax := range components.Taxes {
			if tax.Value != nil && *tax.Value < 0 {
				*errors = append(*errors, validation.ValidationError{
					Type:       TaxError,
					ResourceID: *tax.ProviderId,
					Reason:     "tax value cannot be negative",
				})
			}
		}
	}

	// Validate charges
	if components.Charges != nil {
		for _, charge := range components.Charges {
			if charge.Value != nil && *charge.Value < 0 {
				*errors = append(*errors, validation.ValidationError{
					Type:       ChargeError,
					ResourceID: *charge.ProviderId,
					Reason:     "charge value cannot be negative",
				})
			}

			// Check if charge type is valid
			if charge.Type != nil && !utils.SliceContains(constants.AllowedChargeTypes, *charge.Type) {
				*errors = append(*errors, validation.ValidationError{
					Type:       ChargeError,
					ResourceID: *charge.ProviderId,
					Reason:     fmt.Sprintf("invalid charge type. Allowed types: %v", constants.AllowedChargeTypes),
				})
			}
		}
	}
}
