package order

import (
	"fmt"
	"strings"

	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/types/validation"
)

const (
	OrderInfoError string = "orderInfo"
	PaymentError   string = "payment"
	CustomerError  string = "customer"
	OrderItemError string = "orderItem"
	VariantError   string = "variant"
	AddOnError     string = "addOn"
	TaxError       string = "tax"
	ChargeError    string = "charge"
)

// ValidateOrder performs all order validations and returns any validation errors
func ValidateOrder(order *orders.Order) []validation.ValidationError {
	var errors []validation.ValidationError

	// Validate order info
	validateOrderInfo(&order.OrderInfo, &errors)

	// Validate payment
	validatePayment(&order.Payment, &order.OrderInfo, &errors)

	// Validate customer
	validateCustomer(&order.Customer, &errors)

	// Validate order items
	validateOrderItems(order.Items, &errors)

	return errors
}

// validateOrderInfo validates order information
func validateOrderInfo(orderInfo *orders.OrderInfo, errors *[]validation.ValidationError) {
	// Check if order ID is empty (REQUIRED)
	if strings.TrimSpace(orderInfo.OrderID) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: "orderId",
			Reason:     "orderId is required and cannot be empty",
		})
	}

	// Check if restaurant ID is empty (REQUIRED)
	if strings.TrimSpace(orderInfo.RestID) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: "restId",
			Reason:     "restId is required and cannot be empty",
		})
	}

	// Validate status (REQUIRED) - must be "placed" according to spec
	if strings.TrimSpace(orderInfo.Status) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: "status",
			Reason:     "status is required and cannot be empty",
		})
	} else if orderInfo.Status != "placed" {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: "status",
			Reason:     "status must be 'placed'",
		})
	}

	// Validate createdAt (REQUIRED) - must be Unix timestamp
	if orderInfo.CreatedAt <= 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: "createdAt",
			Reason:     "createdAt is required and must be a valid Unix timestamp",
		})
	}

	// Validate deliveryMode (OPTIONAL) - if provided, must be valid enum
	if orderInfo.DeliveryMode != "" {
		validDeliveryModes := []string{"self_delivery", "delivery"}
		if !isValidDeliveryMode(orderInfo.DeliveryMode, validDeliveryModes) {
			*errors = append(*errors, validation.ValidationError{
				Type:       OrderInfoError,
				ResourceID: "deliveryMode",
				Reason:     "deliveryMode must be one of: self_delivery, delivery",
			})
		}
	}

	// Validate monetary values
	if orderInfo.SubTotal < 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: orderInfo.OrderID,
			Reason:     "subtotal cannot be negative",
		})
	}

	if orderInfo.Total < 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: orderInfo.OrderID,
			Reason:     "total cannot be negative",
		})
	}

	if orderInfo.TotalTaxes < 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: orderInfo.OrderID,
			Reason:     "total taxes cannot be negative",
		})
	}

	// DeliveryCharge and PackingCharge fields were removed from unified order structure
	// Charges are now validated at the item level

	if orderInfo.TotalPackingCharge < 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: orderInfo.OrderID,
			Reason:     "total packing charge cannot be negative",
		})
	}

	// Check if total is at least equal to subtotal
	if orderInfo.Total < orderInfo.SubTotal {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderInfoError,
			ResourceID: orderInfo.OrderID,
			Reason:     "total cannot be less than subtotal",
		})
	}

	// Validate delivery mode if present (Type field was removed from unified order structure)
	if orderInfo.DeliveryMode != "" {
		validDeliveryModes := []string{"delivery", "self_delivery"}
		if !isValidDeliveryMode(orderInfo.DeliveryMode, validDeliveryModes) {
			*errors = append(*errors, validation.ValidationError{
				Type:       OrderInfoError,
				ResourceID: orderInfo.OrderID,
				Reason:     "invalid delivery mode, must be one of: delivery, self_delivery (case-insensitive)",
			})
		}
	}
}

// validatePayment validates payment information
func validatePayment(payment *orders.Payment, orderInfo *orders.OrderInfo, errors *[]validation.ValidationError) {
	// Validate payment mode (REQUIRED) - according to spec, "online" is valid
	if strings.TrimSpace(payment.Mode) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       PaymentError,
			ResourceID: "mode",
			Reason:     "payment mode is required and cannot be empty",
		})
	} else {
		validPaymentModes := []string{"online", "cash", "card", "upi"}
		if !isValidPaymentMode(payment.Mode, validPaymentModes) {
			*errors = append(*errors, validation.ValidationError{
				Type:       PaymentError,
				ResourceID: "mode",
				Reason:     "payment mode must be one of: online, cash, card, upi",
			})
		}
	}

	// Validate payment status (REQUIRED) - according to spec, must be "paid"
	if strings.TrimSpace(payment.Status) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       PaymentError,
			ResourceID: "status",
			Reason:     "payment status is required and cannot be empty",
		})
	} else if payment.Status != "paid" {
		*errors = append(*errors, validation.ValidationError{
			Type:       PaymentError,
			ResourceID: "status",
			Reason:     "payment status must be 'paid'",
		})
	}

	// Validate amountPaid (REQUIRED)
	if payment.AmountPaid <= 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       PaymentError,
			ResourceID: "amountPaid",
			Reason:     "amountPaid is required and must be greater than 0",
		})
	}

	// AmountBalance field was removed from unified order structure
	// Balance can be calculated as (total - amountPaid) if needed

	// For paid orders, validate amount paid equals total
	if payment.Status == "paid" && payment.AmountPaid != orderInfo.Total {
		*errors = append(*errors, validation.ValidationError{
			Type:       PaymentError,
			ResourceID: "amountPaid",
			Reason:     "for paid orders, amountPaid must equal total amount",
		})
	}
}

// validateCustomer validates customer information
func validateCustomer(customer *orders.Customer, errors *[]validation.ValidationError) {
	// Check if first name is empty (REQUIRED)
	if strings.TrimSpace(customer.FirstName) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       CustomerError,
			ResourceID: "firstName",
			Reason:     "firstName is required and cannot be empty",
		})
	}

	// Check if last name is empty (REQUIRED)
	if strings.TrimSpace(customer.LastName) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       CustomerError,
			ResourceID: "lastName",
			Reason:     "lastName is required and cannot be empty",
		})
	}
}

// validateOrderItems validates all order items
func validateOrderItems(items []orders.OrderItem, errors *[]validation.ValidationError) {
	// Check if at least one item is present (REQUIRED)
	if len(items) == 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderItemError,
			ResourceID: "item",
			Reason:     "item array is required and must contain at least one item",
		})
		return
	}

	// Validate each item
	for i, item := range items {
		validateOrderItem(&item, i, errors)
	}
}

// validateOrderItem validates a single order item
func validateOrderItem(item *orders.OrderItem, itemIndex int, errors *[]validation.ValidationError) {
	itemPrefix := fmt.Sprintf("item[%d]", itemIndex)

	// Check if item ID is empty (REQUIRED)
	if strings.TrimSpace(item.ItemID) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderItemError,
			ResourceID: fmt.Sprintf("%s.itemId", itemPrefix),
			Reason:     "itemId is required and cannot be empty",
		})
	}

	// Check if item name is empty (REQUIRED)
	if strings.TrimSpace(item.Name) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderItemError,
			ResourceID: fmt.Sprintf("%s.name", itemPrefix),
			Reason:     "name is required and cannot be empty",
		})
	}

	// Check if quantity is positive (REQUIRED)
	if item.Quantity <= 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderItemError,
			ResourceID: fmt.Sprintf("%s.quantity", itemPrefix),
			Reason:     "quantity is required and must be greater than 0",
		})
	}

	// Check if unit price is non-negative (REQUIRED)
	if item.UnitPrice < 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       OrderItemError,
			ResourceID: fmt.Sprintf("%s.unitPrice", itemPrefix),
			Reason:     "unitPrice is required and cannot be negative",
		})
	}

	// Validate variants if present
	if len(item.Variants) > 0 {
		validateVariants(item.Variants, item.ItemID, errors)
	}

	// Validate addons if present
	if item.AddOns != nil {
		validateAddOns(item.AddOns, item.ItemID, errors)
	}

	// Validate taxes
	validateTaxes(item.Taxes, item.ItemID, errors)

	// Validate charges
	validateCharges(item.Charges, item.ItemID, errors)
}

// validateVariants validates array of variants
func validateVariants(variants []orders.Variant, itemID string, errors *[]validation.ValidationError) {
	for _, variant := range variants {
		validateVariant(&variant, itemID, errors)
	}
}

// validateVariant validates variant information
func validateVariant(variant *orders.Variant, itemID string, errors *[]validation.ValidationError) {
	// Check if variant ID is empty
	if strings.TrimSpace(variant.ID) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       VariantError,
			ResourceID: itemID,
			Reason:     "variant ID cannot be empty",
		})
	}

	// Check if variant name is empty
	if strings.TrimSpace(variant.Name) == "" {
		*errors = append(*errors, validation.ValidationError{
			Type:       VariantError,
			ResourceID: variant.ID,
			Reason:     "variant name cannot be empty",
		})
	}

	// Check if unit price is non-negative
	if variant.UnitPrice < 0 {
		*errors = append(*errors, validation.ValidationError{
			Type:       VariantError,
			ResourceID: variant.ID,
			Reason:     "variant unit price cannot be negative",
		})
	}

	// Taxes and Charges fields were removed from Variant struct in unified order structure
	// Taxes and charges are now only validated at the item level

	// Recursively validate nested variants
	if len(variant.Variants) > 0 {
		validateVariants(variant.Variants, variant.ID, errors)
	}

	// Validate variant addons
	if variant.AddOns != nil {
		validateAddOns(variant.AddOns, variant.ID, errors)
	}
}

// validateAddOns validates addon information
func validateAddOns(addOns []orders.AddOn, parentID string, errors *[]validation.ValidationError) {
	for _, addOn := range addOns {
		// Check if addon ID is empty
		if strings.TrimSpace(addOn.ID) == "" {
			*errors = append(*errors, validation.ValidationError{
				Type:       AddOnError,
				ResourceID: parentID,
				Reason:     "addon ID cannot be empty",
			})
		}

		// Check if addon name is empty
		if strings.TrimSpace(addOn.Name) == "" {
			*errors = append(*errors, validation.ValidationError{
				Type:       AddOnError,
				ResourceID: addOn.ID,
				Reason:     "addon name cannot be empty",
			})
		}

		// Check if unit price is non-negative
		if addOn.UnitPrice < 0 {
			*errors = append(*errors, validation.ValidationError{
				Type:       AddOnError,
				ResourceID: addOn.ID,
				Reason:     "addon unit price cannot be negative",
			})
		}

		// Taxes, Charges, and nested AddOns fields were removed from AddOn struct in unified order structure
		// Taxes and charges are now only validated at the item level
		// AddOns no longer have nested structure
	}
}

// validateTaxes validates tax information
func validateTaxes(taxes []orders.Tax, itemID string, errors *[]validation.ValidationError) {
	for i, tax := range taxes {
		taxPrefix := fmt.Sprintf("%s.taxes[%d]", itemID, i)

		// Check if tax title is empty (REQUIRED)
		if strings.TrimSpace(tax.Title) == "" {
			*errors = append(*errors, validation.ValidationError{
				Type:       TaxError,
				ResourceID: fmt.Sprintf("%s.title", taxPrefix),
				Reason:     "tax title is required and cannot be empty",
			})
		}

		// Check if tax value is non-negative (REQUIRED)
		if tax.Value < 0 {
			*errors = append(*errors, validation.ValidationError{
				Type:       TaxError,
				ResourceID: fmt.Sprintf("%s.value", taxPrefix),
				Reason:     "tax value is required and cannot be negative",
			})
		}

		// Check if tax percentage is non-negative (REQUIRED)
		if tax.Percentage < 0 {
			*errors = append(*errors, validation.ValidationError{
				Type:       TaxError,
				ResourceID: fmt.Sprintf("%s.percentage", taxPrefix),
				Reason:     "tax percentage is required and cannot be negative",
			})
		}

		// Check if liability_on is valid (REQUIRED)
		if strings.TrimSpace(tax.LiabilityOn) == "" {
			*errors = append(*errors, validation.ValidationError{
				Type:       TaxError,
				ResourceID: fmt.Sprintf("%s.liability_on", taxPrefix),
				Reason:     "liability_on is required and cannot be empty",
			})
		} else {
			validLiabilities := []string{"restaurant", "aggregator"}
			if !isValidLiability(tax.LiabilityOn, validLiabilities) {
				*errors = append(*errors, validation.ValidationError{
					Type:       TaxError,
					ResourceID: fmt.Sprintf("%s.liability_on", taxPrefix),
					Reason:     "liability_on must be one of: restaurant, aggregator",
				})
			}
		}
	}
}

// validateCharges validates charge information
func validateCharges(charges []orders.Charge, itemID string, errors *[]validation.ValidationError) {
	for i, charge := range charges {
		chargePrefix := fmt.Sprintf("%s.charges[%d]", itemID, i)

		// Check if charge title is empty (REQUIRED)
		if strings.TrimSpace(charge.Title) == "" {
			*errors = append(*errors, validation.ValidationError{
				Type:       ChargeError,
				ResourceID: fmt.Sprintf("%s.title", chargePrefix),
				Reason:     "charge title is required and cannot be empty",
			})
		}

		// Check if charge value is non-negative (REQUIRED)
		if charge.Value < 0 {
			*errors = append(*errors, validation.ValidationError{
				Type:       ChargeError,
				ResourceID: fmt.Sprintf("%s.value", chargePrefix),
				Reason:     "charge value is required and cannot be negative",
			})
		}

		// Check if liability_on is valid (REQUIRED)
		if strings.TrimSpace(charge.LiabilityOn) == "" {
			*errors = append(*errors, validation.ValidationError{
				Type:       ChargeError,
				ResourceID: fmt.Sprintf("%s.liability_on", chargePrefix),
				Reason:     "liability_on is required and cannot be empty",
			})
		} else {
			validLiabilities := []string{"restaurant", "aggregator"}
			if !isValidLiability(charge.LiabilityOn, validLiabilities) {
				*errors = append(*errors, validation.ValidationError{
					Type:       ChargeError,
					ResourceID: fmt.Sprintf("%s.liability_on", chargePrefix),
					Reason:     "liability_on must be one of: restaurant, aggregator",
				})
			}
		}
	}
}

// Helper functions for validation
func isValidOrderType(orderType string, validTypes []string) bool {
	// Convert to lowercase for case-insensitive comparison
	orderTypeLower := strings.ToLower(orderType)
	for _, validType := range validTypes {
		if orderTypeLower == validType {
			return true
		}
	}
	return false
}

func isValidDeliveryMode(deliveryMode string, validModes []string) bool {
	// Convert to lowercase for case-insensitive comparison
	deliveryModeLower := strings.ToLower(deliveryMode)
	for _, validMode := range validModes {
		if deliveryModeLower == validMode {
			return true
		}
	}
	return false
}

func isValidPaymentMode(paymentMode string, validModes []string) bool {
	// Convert to lowercase for case-insensitive comparison
	paymentModeLower := strings.ToLower(paymentMode)
	for _, validMode := range validModes {
		if paymentModeLower == validMode {
			return true
		}
	}
	return false
}

func isValidPaymentStatus(paymentStatus string, validStatuses []string) bool {
	// Convert to lowercase for case-insensitive comparison
	paymentStatusLower := strings.ToLower(paymentStatus)
	for _, validStatus := range validStatuses {
		if paymentStatusLower == validStatus {
			return true
		}
	}
	return false
}

func isValidLiability(liability string, validLiabilities []string) bool {
	// Convert to lowercase for case-insensitive comparison
	liabilityLower := strings.ToLower(liability)
	for _, validLiability := range validLiabilities {
		if liabilityLower == validLiability {
			return true
		}
	}
	return false
}
