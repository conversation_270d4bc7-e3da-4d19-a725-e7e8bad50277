package common

import "github.com/nutanalabs/pos-gateway/internal/types/validation"

// MenuPushStatus represents the status of a menu push operation from pos provider
type MenuProcessingStatus struct {
	RequestID string                       `json:"requestId"`           // Required
	Success   bool                         `json:"success"`             // Required
	ErrorMsgs []string                     `json:"errorMsgs,omitempty"` // Optional
	Data      []validation.ValidationError `json:"data,omitempty"`      // Optional
}

type Error struct {
	ResourceID string `json:"resourceId"` // Required
	Type       string `json:"type"`       // Required; allowed: category, subcategory, item, variantGroup, addonGroup, variant, addon, tax, charge
	Reason     string `json:"reason"`     // Required
}
