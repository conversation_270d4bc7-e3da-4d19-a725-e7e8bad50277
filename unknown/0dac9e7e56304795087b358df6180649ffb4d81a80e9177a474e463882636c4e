package kafka

import (
	"errors"
	"fmt"

	"github.com/confluentinc/confluent-kafka-go/kafka"
	"github.com/nutanalabs/pos-gateway/internal/config"
	logger "github.com/roppenlabs/rapido-logger-go"
)

type ProducerInterface interface {
	SendMessage(topic string, message []byte) error
	SendKeyedMessage(topic string, key string, message []byte) error
	CheckBrokers() error
	Close()
}

type Producer struct {
	producer *kafka.Producer
}

func NewKafkaProducerInstance(config *config.Config) (ProducerInterface, error) {
	producer, err := newKafkaProducer(config)

	return &Producer{producer: producer}, err
}

func (k *Producer) SendMessage(topic string, message []byte) error {

	err := k.producer.Produce(&kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Value:          message,
	}, nil)

	if err != nil {
		logger.Error(logger.Format{
			Event:   "KAFKA_SEND_MESSAGE_FAILED",
			Message: fmt.Sprintf("Failed to send message to Kafka server: %v", err),
			Data:    map[string]string{"topic": topic},
		})
		return err
	}

	return nil
}

func (k *Producer) SendKeyedMessage(topic string, key string, message []byte) error {

	err := k.producer.Produce(&kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Value:          message,
		Key:            []byte(key),
	}, nil)

	if err != nil {
		logger.Error(logger.Format{
			Event:   "KAFKA_SEND_KEYED_MESSAGE_FAILED",
			Message: fmt.Sprintf("Failed to send keyed message to Kafka server: %v", err),
			Data:    map[string]string{"topic": topic, "key": key},
		})
		return err
	}

	return nil
}

func (k *Producer) CheckBrokers() error {
	var topicName = ""
	metaData, err := k.producer.GetMetadata(&topicName, false, 1000)

	if err != nil {
		logger.Error(logger.Format{
			Event:   "KAFKA_CHECK_BROKERS_FAILED",
			Message: fmt.Sprintf("Failed to get metadata for test topic: %v", err),
		})
		return errors.New("kafkaHealthCheckError: Could not get kafka broker list")
	}

	brokersMetadata := metaData.Brokers
	if brokersMetadata == nil || len(brokersMetadata) <= 0 {
		logger.Error(logger.Format{
			Event:   "KAFKA_CHECK_BROKERS_FAILED",
			Message: fmt.Sprintf("Failed to read brokers data from Kafka: %v", brokersMetadata),
		})
		return errors.New("kafkaHealthCheckError: Could not get kafka broker list")
	}
	return nil
}

func (k *Producer) Close() {
	if k.producer != nil {
		k.producer.Close()
		logger.Info(logger.Format{
			Event:   "KAFKA_CLOSE",
			Message: "Closed Kafka Producer & Consumer Connection",
		})
	}
}

func newKafkaProducer(config *config.Config) (*kafka.Producer, error) {

	kafkaConfig := config.GetKafkaConfig()
	producer, err := kafka.NewProducer(&kafka.ConfigMap{"bootstrap.servers": kafkaConfig.BootstrapServers})

	if err != nil {
		logger.Error(logger.Format{
			Event:   "KAFKA_NEW_PRODUCER_FAILED",
			Message: fmt.Sprintf("Failed to create new producer on kafka: %v", err),
			Data:    map[string]string{"config": kafkaConfig.BootstrapServers},
		})
		return nil, err
	}

	logger.Info(logger.Format{Message: "Successfully connected to kafka as a producer"})

	// Delivery report handler for produced messages
	go func() {
		for e := range producer.Events() {
			switch ev := e.(type) {
			case *kafka.Message:
				if ev.TopicPartition.Error != nil {
					fmt.Printf("Delivery failed: %v\n", ev.TopicPartition.Error)
				}
			}
		}
	}()

	return producer, nil
}
