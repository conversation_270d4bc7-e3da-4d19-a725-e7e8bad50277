package handler

//
//import (
//	"github.com/gin-gonic/gin"
//	"github.com/nutanalabs/pos-gateway/internal/constants"
//)
//
//func (h *Handler) UrbanPiperRoutes(router *gin.Engine) {
//
//	router.POST("/api/v1/urbanpiper/order-status", func(context *gin.Context) {
//		context.JSON(200, gin.H{
//			"message": "order status updated successfully",
//		})
//	})
//	// update item
//	router.POST("/api/v1/urbanpiper/item-status", func(context *gin.Context) {
//		var reqBody map[string]interface{}
//		if err := context.ShouldBindJSON(&reqBody); err != nil {
//			context.JSON(400, gin.H{
//				"error": "invalid request body",
//			})
//			return
//		}
//		h.service.PushItemInventoryStatus(&reqBody, constants.UrbanpiperClientId)
//		context.JSON(200, gin.H{
//			"message": "item status updated successfully",
//		})
//	})
//	// update store staus
//	router.POST("/api/v1/urbanpiper/store-status", func(context *gin.Context) {
//		context.JSON(200, gin.H{
//			"message": "store status updated successfully",
//		})
//	})
//
//}
