package urbanpiper

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// UrbanpiperAdapter implements the POSAdapter interface for Urbanpiper
type UrbanpiperAdapter struct {
	client *Client
}

// NewUrbanpiperAdapter creates a new Urbanpiper adapter instance
func NewUrbanpiperAdapter(client *Client) adapter.POSAdapter {
	return &UrbanpiperAdapter{
		client: client,
	}
}

// SendOrder sends order details to Urbanpiper
func (u *UrbanpiperAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	transformedOrder := TransformOrder(order)
	_, err := u.client.SendOrder(transformedOrder)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to UrbanPiper successfully",
	}, nil
}

// SendRiderDetails sends rider details to Urbanpiper
func (u *UrbanpiperAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	transformedRider := TransformRider(rider)
	_, err := u.client.SendRiderDetails(transformedRider)
	return types.SuccessResponse{}, err
}

// SendMenuProcessingRequestStatus sends menu processing status to Urbanpiper
func (u *UrbanpiperAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (types.SuccessResponse, error) {
	_, err := u.client.SendMenuProcessingRequestStatus(status, callbackURL)
	return types.SuccessResponse{}, err
}

// TransformOrder transforms unified order to UrbanPiper order format using centralized transformation
func TransformOrder(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to UrbanPiper format using struct-based approach
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.UrbanpiperClientId)

	return transformedOrder
}

// TransformRider transforms unified rider to UrbanPiper rider format
func TransformRider(rider orders.Rider) interface{} {
	// TODO: Implement UrbanPiper rider transformation
	return rider
}
