package transformers

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	urbanpiperTypes "github.com/nutanalabs/pos-gateway/internal/posclients/urbanpiper/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/types/urbanpiper"
)

// TransformUrbanPiperMenu converts Urban Piper menu format to common format
func (t *transformersImpl) TransformUrbanPiperMenu(menu *map[string]interface{}) *common.UnifiedMenu {
	var urbanPiperMenu urbanpiper.Menu
	err := utils.UnmarshalJSONToInterface(menu, &urbanPiperMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling urbanpiper menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}
	if urbanPiperMenu.Menu == nil {
		return nil
	}

	// Create maps for quick lookup of charges and taxes by ID
	chargesMap := make(map[string]urbanpiper.Charge)
	taxesMap := make(map[string]urbanpiper.Tax)
	timingsMap := make(map[string]urbanpiper.Timing)

	if urbanPiperMenu.BillComponents != nil {
		for _, charge := range urbanPiperMenu.BillComponents.Charges {
			if charge.RefID != nil {
				chargesMap[*charge.RefID] = charge
			}
		}
		for _, tax := range urbanPiperMenu.BillComponents.Taxes {
			if tax.RefID != nil {
				taxesMap[*tax.RefID] = tax
			}
		}
	}

	// Create map of timings by ID
	for _, timing := range urbanPiperMenu.Timings {
		if timing.RefID != nil {
			timingsMap[*timing.RefID] = timing
		}
	}

	// Collect all addon groups from items and variants
	addonGroupsMap := make(map[string]urbanpiper.AddOnGroup)
	collectAddonGroups(urbanPiperMenu.Menu.Categories, addonGroupsMap)

	// Collect all variant groups and variants
	variantGroupsMap := make(map[string]urbanpiper.VariantGroup)
	variantsMap := make(map[string]urbanpiper.Variant)
	collectVariantGroupsAndVariants(urbanPiperMenu.Menu.Categories, variantGroupsMap, variantsMap)

	// Transform all timings to root level
	allTimings := transformUrbanPiperTimings(urbanPiperMenu.Timings)

	unifiedMenu := &common.UnifiedMenu{
		Categories:          transformUrbanPiperCategories(urbanPiperMenu.Menu.Categories, chargesMap, taxesMap, timingsMap),
		Timings:             allTimings,
		OrderBillComponents: transformUrbanPiperOrderBillComponents(urbanPiperMenu.OrderBillComponents, chargesMap),
		BillComponents: &common.BillComponents{
			Charges: transformUrbanPiperCharges(urbanPiperMenu.BillComponents.Charges),
			Taxes:   transformUrbanPiperTaxes(urbanPiperMenu.BillComponents),
		},
		CallbackURL: urbanPiperMenu.CallbackURL,
	}

	// Add restaurant details if available
	if urbanPiperMenu.Location != nil {
		unifiedMenu.Restaurant = &common.Restaurant{
			ProviderId:           urbanPiperMenu.Location.RefID,
			OrderingEnabled:      true,
			MinDeliveryTime:      urbanPiperMenu.Location.MinDeliveryTime,
			Provider:             constants.UrbanpiperProvider,
			ProviderAbbreviation: constants.ProviderAbbreviationMap[constants.UrbanpiperProvider],
			Timings:              urbanPiperMenu.Location.Timings,
		}
	}

	// Transform addon groups to root level
	unifiedMenu.AddOnGroups = transformUrbanPiperAddOnGroupsToRoot(addonGroupsMap)

	// Transform variant groups and variants to root level
	unifiedMenu.VariantGroups, unifiedMenu.Variants = transformUrbanPiperVariantGroupsAndVariantsToRoot(variantGroupsMap, variantsMap, chargesMap, taxesMap)

	return unifiedMenu
}

// collectAddonGroups recursively collects all addon groups from categories, items, and variants
func collectAddonGroups(categories []urbanpiper.Category, addonGroupsMap map[string]urbanpiper.AddOnGroup) {
	for _, category := range categories {
		// Process items in current category
		for _, item := range category.Items {
			// Collect addon groups from item
			if item.AddOnGroups != nil {
				for _, group := range item.AddOnGroups {
					if group.RefID != nil {
						addonGroupsMap[*group.RefID] = group
					}
				}
			}

			// Collect addon groups from variants and their nested variant groups
			if item.VariantGroups != nil {
				for _, variantGroup := range item.VariantGroups {
					collectAddonGroupsFromVariantGroup(variantGroup, addonGroupsMap)
				}
			}
		}

		// Recursively process subcategories
		if category.Subcategories != nil {
			collectAddonGroups(category.Subcategories, addonGroupsMap)
		}
	}
}

// collectAddonGroupsFromVariantGroup recursively collects addon groups from a variant group and its nested variant groups
func collectAddonGroupsFromVariantGroup(variantGroup urbanpiper.VariantGroup, addonGroupsMap map[string]urbanpiper.AddOnGroup) {
	// Process each variant in the variant group
	for _, variant := range variantGroup.Variants {
		// Collect addon groups from the variant
		if variant.AddOnGroups != nil {
			for _, group := range variant.AddOnGroups {
				if group.RefID != nil {
					addonGroupsMap[*group.RefID] = group
				}
			}
		}

		// Recursively process nested variant groups
		if variant.VariantGroups != nil {
			for _, nestedGroup := range variant.VariantGroups {
				collectAddonGroupsFromVariantGroup(nestedGroup, addonGroupsMap)
			}
		}
	}
}

// collectVariantGroupsAndVariants recursively collects all variant groups and variants
func collectVariantGroupsAndVariants(categories []urbanpiper.Category, variantGroupsMap map[string]urbanpiper.VariantGroup, variantsMap map[string]urbanpiper.Variant) {
	for _, category := range categories {
		// Process items in current category
		for _, item := range category.Items {
			// Collect variant groups and variants from item
			if item.VariantGroups != nil {
				for _, group := range item.VariantGroups {
					if group.RefID != nil {
						variantGroupsMap[*group.RefID] = group
					}
					for _, variant := range group.Variants {
						if variant.RefID != nil {
							variantsMap[*variant.RefID] = variant
						}
						// Collect nested variant groups
						if variant.VariantGroups != nil {
							for _, nestedGroup := range variant.VariantGroups {
								if nestedGroup.RefID != nil {
									variantGroupsMap[*nestedGroup.RefID] = nestedGroup
								}
								// Collect variants from nested groups
								for _, nestedVariant := range nestedGroup.Variants {
									if nestedVariant.RefID != nil {
										variantsMap[*nestedVariant.RefID] = nestedVariant
									}
								}
							}
						}
					}
				}
			}
		}

		// Recursively process subcategories
		if category.Subcategories != nil {
			collectVariantGroupsAndVariants(category.Subcategories, variantGroupsMap, variantsMap)
		}
	}
}

// transformUrbanPiperCategories converts Urban Piper categories to common format
func transformUrbanPiperCategories(categories []urbanpiper.Category, chargesMap map[string]urbanpiper.Charge, taxesMap map[string]urbanpiper.Tax, timingsMap map[string]urbanpiper.Timing) []common.Category {
	if len(categories) == 0 {
		return nil
	}

	commonCategories := make([]common.Category, 0, len(categories))
	for _, category := range categories {
		sortOrder := 0 // Default sort order based on array index if not provided
		sortOrder = category.SortOrder
		providerId := *category.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		commonCategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          category.Title,
			Description:   category.Description,
			ImageURL:      category.ImageURL,
			SortOrder:     sortOrder,
			Items:         transformUrbanPiperItems(category.Items, chargesMap, taxesMap),
			Subcategories: transformUrbanPiperSubcategories(category.Subcategories, chargesMap, taxesMap, timingsMap),
			Timings:       transformUrbanPiperCategoryTimings(category.Timings, timingsMap),
		}
		commonCategories = append(commonCategories, commonCategory)
	}

	return commonCategories
}

// transformUrbanPiperSubcategories converts Urban Piper subcategories to common format
func transformUrbanPiperSubcategories(subcategories []urbanpiper.Category, chargesMap map[string]urbanpiper.Charge, taxesMap map[string]urbanpiper.Tax, timingsMap map[string]urbanpiper.Timing) []common.Category {
	if len(subcategories) == 0 {
		return nil
	}

	commonSubcategories := make([]common.Category, 0, len(subcategories))
	for _, subcategory := range subcategories {
		sortOrder := 0 // Default sort order based on array index if not provided
		sortOrder = subcategory.SortOrder

		providerId := *subcategory.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		commonSubcategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          subcategory.Title,
			Description:   subcategory.Description,
			ImageURL:      subcategory.ImageURL,
			SortOrder:     sortOrder,
			Items:         transformUrbanPiperItems(subcategory.Items, chargesMap, taxesMap),
			Subcategories: transformUrbanPiperSubcategories(subcategory.Subcategories, chargesMap, taxesMap, timingsMap),
			Timings:       transformUrbanPiperCategoryTimings(subcategory.Timings, timingsMap),
		}
		commonSubcategories = append(commonSubcategories, commonSubcategory)
	}

	return commonSubcategories
}

// transformUrbanPiperCategoryTimings converts Urban Piper category timings to common format
func transformUrbanPiperCategoryTimings(timingIDs *string, timingsMap map[string]urbanpiper.Timing) *string {
	if timingIDs == nil || *timingIDs == "" {
		return nil
	}

	// Split comma-separated timing IDs
	ids := strings.Split(*timingIDs, ",")
	if len(ids) == 0 {
		return nil
	}

	// Use the first timing ID as the reference
	timingID := strings.TrimSpace(ids[0])
	if timing, exists := timingsMap[timingID]; exists && timing.RefID != nil {
		providerId := *timing.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
		return &id
	}

	return nil
}

// transformUrbanPiperItems converts Urban Piper items to common format
func transformUrbanPiperItems(items []urbanpiper.Item, chargesMap map[string]urbanpiper.Charge, taxesMap map[string]urbanpiper.Tax) []common.Item {
	if len(items) == 0 {
		return nil
	}

	commonItems := make([]common.Item, 0, len(items))
	for _, item := range items {
		var sortOrder int
		sortOrder = item.SortOrder
		// Transform food type
		foodType := constants.FoodTypeNotSpecified // Default value
		if item.FoodType != nil {
			if internalType, ok := constants.UrbanPiperFoodTypesMapToInternal[*item.FoodType]; ok {
				foodType = internalType
			}
		}

		// Transform variant group IDs
		variantGroupIDs := make([]string, 0)
		if item.VariantGroups != nil {
			for _, group := range item.VariantGroups {
				if group.RefID != nil {
					providerId := *group.RefID
					id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
					variantGroupIDs = append(variantGroupIDs, id)
				}
			}
		}

		// Transform addon group IDs
		addOnGroupIDs := make([]string, 0)
		if item.AddOnGroups != nil {
			for _, group := range item.AddOnGroups {
				if group.RefID != nil {
					providerId := *group.RefID
					id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
					addOnGroupIDs = append(addOnGroupIDs, id)
				}
			}
		}

		providerId := *item.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		// Transform category and subcategory IDs
		var categoryID, subcategoryID *string
		if item.CategoryRefID != nil {
			catProviderId := *item.CategoryRefID
			catId := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + catProviderId
			categoryID = &catId
		}
		if item.SubCategoryRefID != nil {
			subcatProviderId := *item.SubCategoryRefID
			subcatId := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + subcatProviderId
			subcategoryID = &subcatId
		}

		commonItem := common.Item{
			ID:               &id,
			ProviderId:       &providerId,
			Name:             item.Title,
			Description:      item.Description,
			ImageURL:         item.ImageURL,
			Price:            transformUrbanPiperPrice(item.Price),
			MarkupPrice:      nil, // UrbanPiper doesn't have markup price
			InStock:          item.InStock,
			Recommended:      item.Recommended,
			FoodType:         &foodType,
			Tags:             item.Tags,
			BillComponents:   transformUrbanPiperItemBillComponents(item.BillComponents, chargesMap, taxesMap),
			VariantGroupIDs:  variantGroupIDs,
			AddOnGroupIDs:    addOnGroupIDs,
			NutritionalInfo:  transformUrbanPiperNutritionalInfo(item.NutritionalInfo),
			FulfillmentModes: transformUrbanPiperFulfillmentModes(item.FulfillmentModes),
			SortOrder:        sortOrder,
			CategoryID:       categoryID,
			SubcategoryID:    subcategoryID,
		}
		commonItems = append(commonItems, commonItem)
	}

	return commonItems
}

// transformUrbanPiperPrice converts Urban Piper price to common format
func transformUrbanPiperPrice(price interface{}) *float64 {
	if price == nil {
		return nil
	}

	switch v := price.(type) {
	case float64:
		return &v
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return &f
		}
	}
	return nil
}

// transformUrbanPiperVariantGroupsAndVariantsToRoot transforms variant groups and variants to root level
func transformUrbanPiperVariantGroupsAndVariantsToRoot(groupsMap map[string]urbanpiper.VariantGroup, variantsMap map[string]urbanpiper.Variant, chargesMap map[string]urbanpiper.Charge, taxesMap map[string]urbanpiper.Tax) ([]common.VariantGroup, []common.Variant) {
	commonGroups := make([]common.VariantGroup, 0)
	commonVariants := make([]common.Variant, 0)

	// Transform variant groups
	for _, group := range groupsMap {
		if group.RefID == nil {
			continue
		}

		providerId := *group.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		// Transform variant IDs
		variantIDs := make([]string, 0)
		for _, variant := range group.Variants {
			if variant.RefID != nil {
				variantProviderId := *variant.RefID
				variantId := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + variantProviderId
				variantIDs = append(variantIDs, variantId)
			}
		}

		commonGroup := common.VariantGroup{
			ID:         &id,
			ProviderId: &providerId,
			Name:       group.Title,
			VariantIDs: variantIDs,
			SortOrder:  group.SortOrder,
		}
		commonGroups = append(commonGroups, commonGroup)
	}

	// Transform variants
	for _, variant := range variantsMap {
		if variant.RefID == nil {
			continue
		}

		// Transform food type
		foodType := "not Specified" // Default value
		if variant.FoodType != nil {
			if internalType, ok := constants.UrbanPiperFoodTypesMapToInternal[*variant.FoodType]; ok {
				foodType = internalType
			}
		}

		// Transform addon group IDs
		addOnGroupIDs := make([]string, 0)
		if variant.AddOnGroups != nil {
			for _, group := range variant.AddOnGroups {
				if group.RefID != nil {
					providerId := *group.RefID
					id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
					addOnGroupIDs = append(addOnGroupIDs, id)
				}
			}
		}

		// Transform variant group IDs
		variantGroupIDs := make([]string, 0)
		if variant.VariantGroups != nil {
			for _, group := range variant.VariantGroups {
				if group.RefID != nil {
					providerId := *group.RefID
					id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
					variantGroupIDs = append(variantGroupIDs, id)
				}
			}
		}

		providerId := *variant.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		// Create billComponents for variant
		var billComponents *common.ItemBillComponents
		// In UrbanPiper, variants don't have explicit bill components, but we can assign similar
		// bill components as their parent items if needed in the future

		commonVariant := common.Variant{
			ID:               &id,
			ProviderId:       &providerId,
			Name:             variant.Title,
			Price:            variant.Price,
			MarkupPrice:      nil, // UrbanPiper doesn't have markup price
			InStock:          variant.InStock,
			FoodType:         &foodType,
			AddOnGroupIDs:    addOnGroupIDs,
			NutritionalInfo:  transformUrbanPiperNutritionalInfo(variant.NutritionalInfo),
			FulfillmentModes: transformUrbanPiperFulfillmentModes(variant.FulfillmentModes),
			VariantGroupIDs:  variantGroupIDs,
			BillComponents:   billComponents,
			SortOrder:        variant.SortOrder,
		}
		commonVariants = append(commonVariants, commonVariant)
	}

	return commonGroups, commonVariants
}

// transformUrbanPiperAddOns converts Urban Piper add-ons to common format
func transformUrbanPiperAddOns(addOns []urbanpiper.AddOn) []common.AddOn {
	if len(addOns) == 0 {
		return nil
	}

	commonAddOns := make([]common.AddOn, 0, len(addOns))
	for _, addOn := range addOns {
		if addOn.RefID == nil {
			continue
		}

		// Transform food type
		foodType := "not Specified" // Default value
		if addOn.FoodType != nil {
			if internalType, ok := constants.UrbanPiperFoodTypesMapToInternal[*addOn.FoodType]; ok {
				foodType = internalType
			}
		}

		providerId := *addOn.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		commonAddOn := common.AddOn{
			ID:               &id,
			ProviderId:       &providerId,
			Name:             addOn.Title,
			Price:            addOn.Price,
			InStock:          addOn.InStock,
			FoodType:         &foodType,
			NutritionalInfo:  transformUrbanPiperNutritionalInfo(addOn.NutritionalInfo),
			FulfillmentModes: transformUrbanPiperFulfillmentModes(addOn.FulfillmentModes),
			SortOrder:        addOn.SortOrder,
			ImageURL:         addOn.ImageURL,
		}
		commonAddOns = append(commonAddOns, commonAddOn)
	}

	return commonAddOns
}

// transformUrbanPiperNutritionalInfo converts Urban Piper nutritional info to common format
func transformUrbanPiperNutritionalInfo(info *urbanpiper.NutritionalInfo) *common.NutritionalInfo {
	if info == nil {
		return nil
	}

	return &common.NutritionalInfo{
		Carbohydrate: transformUrbanPiperNutritionalValue(info.Carbohydrate),
		Fiber:        transformUrbanPiperNutritionalValue(info.Fiber),
		TotalFat:     transformUrbanPiperNutritionalValue(info.Fat),
		Protein:      transformUrbanPiperNutritionalValue(info.Protein),
		Calorie:      transformUrbanPiperNutritionalValue(info.Calorie),
		TotalSugar:   transformUrbanPiperNutritionalValue(info.Sugar),
		Sodium:       transformUrbanPiperNutritionalValue(info.Sodium),
		Cholesterol:  transformUrbanPiperNutritionalValue(info.Cholesterol),
		Salt:         transformUrbanPiperNutritionalValue(info.Salt),
	}
}

// transformUrbanPiperNutritionalValue converts Urban Piper nutritional value to common format
func transformUrbanPiperNutritionalValue(value *urbanpiper.NutritionalValue) *common.NutritionalValue {
	if value == nil {
		return nil
	}

	return &common.NutritionalValue{
		Value: value.Value,
		Unit:  value.Unit,
		Name:  nil, // UrbanPiper doesn't have a name field
	}
}

// transformUrbanPiperTimings converts Urban Piper timings to common format
func transformUrbanPiperTimings(timings []urbanpiper.Timing) []common.Timing {
	if len(timings) == 0 {
		return nil
	}

	commonTimings := make([]common.Timing, 0, len(timings))
	for _, timing := range timings {
		if timing.RefID == nil {
			continue
		}
		providerId := *timing.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
		commonTiming := common.Timing{
			ID:   &id,
			Days: transformUrbanPiperDays(timing.Days),
		}
		commonTimings = append(commonTimings, commonTiming)
	}

	return commonTimings
}

// transformUrbanPiperDays converts Urban Piper days to common format
func transformUrbanPiperDays(days []urbanpiper.Day) []common.Day {
	if len(days) == 0 {
		return nil
	}

	commonDays := make([]common.Day, 0, len(days))
	for _, day := range days {
		commonDay := common.Day{
			Day:   day.Day,
			Slots: transformUrbanPiperSlots(day.Slots),
		}
		commonDays = append(commonDays, commonDay)
	}

	return commonDays
}

// transformUrbanPiperSlots converts Urban Piper slots to common format
func transformUrbanPiperSlots(slots []urbanpiper.Slot) []common.Slot {
	if len(slots) == 0 {
		return nil
	}

	commonSlots := make([]common.Slot, 0, len(slots))
	for _, slot := range slots {
		commonSlot := common.Slot{
			StartTime: slot.StartTime,
			EndTime:   slot.EndTime,
		}
		commonSlots = append(commonSlots, commonSlot)
	}

	return commonSlots
}

// transformUrbanPiperTaxes converts Urban Piper taxes to common format
func transformUrbanPiperTaxes(components *urbanpiper.BillComponents) []common.Tax {
	if components == nil || components.Taxes == nil {
		return nil
	}

	commonTaxes := make([]common.Tax, 0)
	for _, tax := range components.Taxes {
		if tax.RefID == nil {
			continue
		}

		providerId := *tax.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		commonTax := common.Tax{
			ID:               &id,
			ProviderId:       &providerId,
			Name:             tax.Title,
			Description:      tax.Description,
			Value:            tax.Value,
			FulfillmentModes: transformUrbanPiperFulfillmentModes(tax.FulfillmentModes),
		}
		commonTaxes = append(commonTaxes, commonTax)
	}

	return commonTaxes
}

// transformUrbanPiperBillComponents converts Urban Piper bill components to common format
func transformUrbanPiperBillComponents(components *urbanpiper.BillComponents) *common.BillComponents {
	if components == nil {
		return nil
	}

	return &common.BillComponents{
		Charges: transformUrbanPiperCharges(components.Charges),
		Taxes:   transformUrbanPiperTaxes(components),
	}
}

// transformUrbanPiperTaxIDs converts Urban Piper tax IDs to common format
func transformUrbanPiperTaxIDs(taxes []urbanpiper.Tax) []string {
	if len(taxes) == 0 {
		return nil
	}

	taxIDs := make([]string, 0, len(taxes))
	for _, tax := range taxes {
		if tax.RefID != nil {
			providerId := *tax.RefID
			id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
			taxIDs = append(taxIDs, id)
		}
	}

	return taxIDs
}

// transformUrbanPiperCharges converts Urban Piper charges to common format
func transformUrbanPiperCharges(charges []urbanpiper.Charge) []common.Charge {
	if len(charges) == 0 {
		return nil
	}

	commonCharges := make([]common.Charge, 0, len(charges))
	for _, charge := range charges {
		if charge.RefID == nil {
			continue
		}
		providerId := *charge.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		// Transform tax IDs to use full ID format
		taxIDs := make([]string, 0)
		for _, taxID := range charge.Taxes {
			taxProviderId := taxID
			taxId := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + taxProviderId
			taxIDs = append(taxIDs, taxId)
		}

		commonCharge := common.Charge{
			ID:               &id,
			ProviderId:       &providerId,
			Name:             charge.Title,
			Description:      charge.Description,
			Value:            charge.Value,
			Type:             charge.Type,
			FulfillmentModes: transformUrbanPiperFulfillmentModes(charge.FulfillmentModes),
			Taxes:            taxIDs,
		}
		commonCharges = append(commonCharges, commonCharge)
	}

	return commonCharges
}

// transformUrbanPiperOrderBillComponents converts Urban Piper order bill components to common format
func transformUrbanPiperOrderBillComponents(components *urbanpiper.OrderBillComponents, chargesMap map[string]urbanpiper.Charge) *common.OrderBillComponents {
	if components == nil {
		return nil
	}

	// Transform charges using the charge IDs
	charges := make([]common.Charge, 0)
	for _, chargeID := range components.Charges {
		if charge, ok := chargesMap[chargeID]; ok {
			providerId := *charge.RefID
			id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

			// Transform tax IDs to use full ID format
			taxIDs := make([]string, 0)
			for _, taxID := range charge.Taxes {
				taxProviderId := taxID
				taxId := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + taxProviderId
				taxIDs = append(taxIDs, taxId)
			}

			commonCharge := common.Charge{
				ID:               &id,
				ProviderId:       &providerId,
				Name:             charge.Title,
				Description:      charge.Description,
				Value:            charge.Value,
				Type:             charge.Type,
				FulfillmentModes: transformUrbanPiperFulfillmentModes(charge.FulfillmentModes),
				Taxes:            taxIDs,
			}
			charges = append(charges, commonCharge)
		}
	}

	if len(charges) > 0 {
		return &common.OrderBillComponents{
			Charges: charges,
		}
	}

	return nil
}

// transformUrbanPiperFulfillmentModes converts Urban Piper fulfillment modes to internal modes
func transformUrbanPiperFulfillmentModes(modes []string) []string {
	if len(modes) == 0 {
		return nil
	}

	fulfillmentModes := make([]string, 0, len(modes))
	for _, mode := range modes {
		if internalMode, ok := constants.UrbanPiperFulfillmentModesMapToInternalMode[mode]; ok {
			fulfillmentModes = append(fulfillmentModes, internalMode)
		}
	}

	return fulfillmentModes
}

// transformUrbanPiperItemBillComponents converts Urban Piper item bill components to common format
func transformUrbanPiperItemBillComponents(components *urbanpiper.ItemBillComponents, chargesMap map[string]urbanpiper.Charge, taxesMap map[string]urbanpiper.Tax) *common.ItemBillComponents {
	if components == nil {
		return nil
	}

	charges := make([]string, 0)
	taxIDs := make([]string, 0)

	// Transform charge IDs
	if len(components.Charges) > 0 {
		for _, chargeID := range components.Charges {
			if _, ok := chargesMap[chargeID]; ok {
				providerId := chargeID
				id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
				charges = append(charges, id)
			}
		}
	}

	// Transform tax IDs
	if len(components.Taxes) > 0 {
		for _, taxID := range components.Taxes {
			if _, ok := taxesMap[taxID]; ok {
				providerId := taxID
				id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId
				taxIDs = append(taxIDs, id)
			}
		}
	}

	if len(charges) > 0 || len(taxIDs) > 0 {
		return &common.ItemBillComponents{
			Charges: charges,
			TaxIDs:  taxIDs,
		}
	}

	return nil
}

// transformUrbanPiperAddOnGroupsToRoot transforms addon groups to root level
func transformUrbanPiperAddOnGroupsToRoot(groupsMap map[string]urbanpiper.AddOnGroup) []common.AddOnGroup {
	commonGroups := make([]common.AddOnGroup, 0)

	for _, group := range groupsMap {
		if group.RefID == nil {
			continue
		}

		providerId := *group.RefID
		id := constants.ProviderAbbreviationMap[constants.UrbanpiperProvider] + providerId

		commonGroup := common.AddOnGroup{
			ID:             &id,
			ProviderId:     &providerId,
			Name:           group.Title,
			MinimumNeeded:  group.MinimumNeeded,
			MaximumAllowed: group.MaximumAllowed,
			AddOns:         transformUrbanPiperAddOns(group.Addons),
			SortOrder:      group.SortOrder,
		}
		commonGroups = append(commonGroups, commonGroup)
	}

	return commonGroups
}

func (t *transformersImpl) TransformUrbanPiperInventory(inventory *map[string]interface{}) *common.InventoryUpdate {
	var urbanpiperInventory urbanpiper.InventoryUpdate
	err := utils.UnmarshalJSONToInterface(inventory, &urbanpiperInventory)
	if err != nil {
		return nil
	}

	return &common.InventoryUpdate{
		RestID:          &urbanpiperInventory.LocationRefID,
		AddOns:          urbanpiperInventory.AddOns,
		InStock:         urbanpiperInventory.InStock,
		Items:           urbanpiperInventory.Items,
		NextAvailableAt: urbanpiperInventory.NextAvailableAt,
		Variants:        urbanpiperInventory.Variants,
	}
}

func (t *transformersImpl) TransformUrbanStoreStatus(storeStatus *map[string]interface{}) *common.StoreStatus {
	var urbanpiperStoreStatus urbanpiper.StoreStatus
	err := utils.UnmarshalJSONToInterface(storeStatus, &urbanpiperStoreStatus)
	if err != nil {
		return nil
	}

	return &common.StoreStatus{
		RestID:          urbanpiperStoreStatus.LocationRefID,
		OrderingEnabled: urbanpiperStoreStatus.OrderingEnabled,
	}
}

// TransformUnifiedOrderToUrbanPiperOrder converts unified order format to UrbanPiper order format
func (t *transformersImpl) TransformUnifiedOrderToUrbanPiperOrder(order *orders.Order) interface{} {
	if order == nil {
		return nil
	}

	// Transform to UrbanPiper order structure using proper structs
	return transformToUrbanPiperOrderStruct(order)
}

// transformToUrbanPiperOrderStruct transforms unified order to UrbanPiper order structure using proper structs
func transformToUrbanPiperOrderStruct(order *orders.Order) interface{} {
	// Use constants for payment and delivery types
	// Derive fulfillment mode from delivery mode since Type field was removed
	fulfillmentMode := transformFulfillmentModeFromDeliveryMode(order.OrderInfo.DeliveryMode)
	paymentMode := transformPaymentMode(order.Payment.Mode)

	// Use restaurant name derived from RestID since Restaurant field was removed
	restaurantName := "Restaurant_" + order.OrderInfo.RestID

	// For UrbanPiper: Use totalPackingCharge from unified order structure for OrderLevelCharges
	orderLevelCharges := order.OrderInfo.TotalPackingCharge

	// Calculate amount balance (total - amount paid)
	amountBalance := order.OrderInfo.Total - order.Payment.AmountPaid

	// Create the UrbanPiper order request using proper structs
	urbanPiperOrder := urbanpiperTypes.SaveOrderRequest{
		Meta: urbanpiperTypes.OrderMeta{
			OrderNo:            order.OrderInfo.OrderID,
			RestaurantName:     restaurantName,
			LocationRefID:      order.OrderInfo.RestID,
			CurrentStatus:      constants.OrderStatusPlaced,
			FulfillmentMode:    fulfillmentMode,
			ASAP:               true,
			DeliveryDatetime:   int64(order.OrderInfo.CreatedAt) * 1000, // Convert to milliseconds
			PickupDatetime:     int64(order.OrderInfo.CreatedAt) * 1000, // Convert to milliseconds
			Total:              order.OrderInfo.Total,
			SubTotal:           order.OrderInfo.SubTotal,
			Created:            int64(order.OrderInfo.CreatedAt), // Keep in seconds
			IsEdit:             false,
			Instructions:       order.OrderInfo.Instruction,
			Charges:            transformOrderChargesStruct(order),
			PrepTimeDetails:    transformPrepTimeDetails(order),
			OrderLevelCharges:  orderLevelCharges,
			IsInstantOrder:     true,
			OrderLevelDiscount: 0.0,
			ItemLevelCharges:   0.0, // Don't calculate any item-level charges for UrbanPiper
			ItemLevelTaxes:     0.0, // Don't calculate any item-level taxes for UrbanPiper
			ItemLevelDiscount:  0.0,
			TotalCharges:       orderLevelCharges, // Use orderLevelCharges (totalPackingCharge)
			TotalTaxes:         order.OrderInfo.TotalTaxes,
			TotalDiscount:      0.0,
			DiscountCode:       "",
		},
		Payment: urbanpiperTypes.PaymentInfo{
			Mode:          paymentMode,
			Status:        order.Payment.Status,
			AmountPaid:    order.Payment.AmountPaid,
			AmountBalance: amountBalance,
		},
		Customer: urbanpiperTypes.CustomerInfo{
			Name:        order.Customer.FirstName + " " + order.Customer.LastName,
			PhoneNumber: "", // Not available in unified order
			Email:       "", // Not available in unified order
			Address:     []urbanpiperTypes.CustomerAddress{},
		},
		Items:     transformOrderItemsStruct(order.Items),
		Discounts: []urbanpiperTypes.Discount{},
	}

	return urbanPiperOrder
}

// transformOrderChargesStruct transforms order charges to UrbanPiper format using structs
// For UrbanPiper: Include all individual charges from each item
func transformOrderChargesStruct(order *orders.Order) []urbanpiperTypes.ItemCharge {
	charges := []urbanpiperTypes.ItemCharge{}

	// For UrbanPiper: Extract all individual charges from each item
	individualCharges := extractAllIndividualCharges(order)
	charges = append(charges, individualCharges...)

	return charges
}

// transformOrderItemsStruct transforms order items to UrbanPiper format using structs
func transformOrderItemsStruct(items []orders.OrderItem) []urbanpiperTypes.OrderItem {
	urbanPiperItems := []urbanpiperTypes.OrderItem{}

	for _, item := range items {
		// Transform item taxes
		itemTaxes := []urbanpiperTypes.ItemTax{}
		for _, tax := range item.Taxes {
			// Use liability from tax or default to restaurant
			liability := tax.LiabilityOn
			if liability == "" {
				liability = constants.TaxLiabilityRestaurant
			}
			itemTaxes = append(itemTaxes, urbanpiperTypes.ItemTax{
				LiabilityOn: liability,
				Title:       tax.Title,
				Value:       tax.Value,
				Percentage:  tax.Percentage,
			})
		}

		// Transform item charges
		itemCharges := []urbanpiperTypes.ItemCharge{}
		for _, charge := range item.Charges {
			itemCharges = append(itemCharges, urbanpiperTypes.ItemCharge{
				Title: charge.Title,
				Value: charge.Value,
			})
		}

		// Transform variants with nested support
		variants := []urbanpiperTypes.ItemVariant{}
		for _, variant := range item.Variants {
			variants = append(variants, transformUrbanPiperVariantStruct(&variant))
		}

		// Transform addons with nested support
		addons := []urbanpiperTypes.ItemAddOn{}
		for _, addon := range item.AddOns {
			addons = append(addons, transformUrbanPiperAddOnStruct(&addon))
		}

		urbanPiperItem := urbanpiperTypes.OrderItem{
			RefID:        item.ItemID,
			Title:        item.Name,
			PricePerUnit: item.UnitPrice,
			Quantity:     item.Quantity,
			Taxes:        itemTaxes,
			Charges:      itemCharges,
			Discount:     0.0, // No discount in unified order
			Total:        item.UnitPrice * float64(item.Quantity),
			Subtotal:     item.UnitPrice * float64(item.Quantity),
			Variants:     variants,
			AddOns:       addons,
		}

		urbanPiperItems = append(urbanPiperItems, urbanPiperItem)
	}

	return urbanPiperItems
}

// Helper functions for struct-based transformation are defined below

// Helper functions for order transformation

// transformOrderStatus transforms unified order status to UrbanPiper status
func transformOrderStatus(status string) string {
	switch strings.ToLower(status) {
	case constants.OrderStatusPending, constants.OrderStatusConfirmed:
		return constants.OrderStatusPlaced
	case constants.OrderStatusAcknowledged:
		return constants.OrderStatusAcknowledged
	case constants.OrderStatusFoodReady:
		return constants.OrderStatusFoodReady
	case constants.OrderStatusDispatched:
		return constants.OrderStatusDispatched
	case constants.OrderStatusCompleted:
		return constants.OrderStatusCompleted
	case constants.OrderStatusCancelled:
		return constants.OrderStatusCancelled
	default:
		return constants.OrderStatusPlaced
	}
}

// transformFulfillmentMode transforms unified order type to UrbanPiper fulfillment mode
func transformFulfillmentMode(orderType string) string {
	switch strings.ToLower(orderType) {
	case constants.DeliveryTypeDelivery:
		return constants.UrbanPiperFulfillmentModeDelivery
	case constants.DeliveryTypeTakeaway:
		return constants.UrbanPiperFulfillmentModePickup
	case constants.DeliveryTypeDineIn:
		return constants.UrbanPiperFulfillmentModePickup // UrbanPiper doesn't have dine-in, map to pickup
	default:
		return constants.UrbanPiperFulfillmentModeDelivery
	}
}

// transformFulfillmentModeFromDeliveryMode transforms delivery mode to UrbanPiper fulfillment mode
func transformFulfillmentModeFromDeliveryMode(deliveryMode string) string {
	switch strings.ToLower(deliveryMode) {
	case "delivery":
		return constants.UrbanPiperFulfillmentModeDelivery
	case "self_delivery":
		return constants.UrbanPiperFulfillmentModePickup
	default:
		return constants.UrbanPiperFulfillmentModeDelivery // Default to delivery
	}
}

// calculateChargesFromOrderUrbanPiper calculates delivery and packing charges from order items
// For backward compatibility - now uses all individual charges for UrbanPiper
func calculateChargesFromOrderUrbanPiper(order *orders.Order) (deliveryCharge, packingCharge float64) {
	// For UrbanPiper: Calculate total of all individual charges by type
	allCharges := extractAllIndividualCharges(order)

	for _, charge := range allCharges {
		chargeTitle := strings.ToLower(charge.Title)
		if strings.Contains(chargeTitle, "delivery") {
			deliveryCharge += charge.Value
		} else if strings.Contains(chargeTitle, "packing") {
			packingCharge += charge.Value
		}
	}

	return deliveryCharge, packingCharge
}

// calculateAllChargesByType calculates charges grouped by type for UrbanPiper
func calculateAllChargesByType(order *orders.Order) map[string]float64 {
	chargesByType := make(map[string]float64)
	allCharges := extractAllIndividualCharges(order)

	for _, charge := range allCharges {
		chargeTitle := strings.ToLower(charge.Title)
		if strings.Contains(chargeTitle, "delivery") {
			chargesByType["delivery"] += charge.Value
		} else if strings.Contains(chargeTitle, "packing") {
			chargesByType["packing"] += charge.Value
		} else if strings.Contains(chargeTitle, "service") {
			chargesByType["service"] += charge.Value
		} else if strings.Contains(chargeTitle, "handling") {
			chargesByType["handling"] += charge.Value
		} else {
			chargesByType["other"] += charge.Value
		}
	}

	return chargesByType
}

// calculateDeliveryChargesFromOrder calculates only delivery charges from order items
func calculateDeliveryChargesFromOrder(order *orders.Order) float64 {
	deliveryCharge := 0.0
	for _, item := range order.Items {
		for _, charge := range item.Charges {
			switch strings.ToLower(charge.Title) {
			case "delivery charge", "delivery":
				deliveryCharge += charge.Value * float64(item.Quantity)
			}
		}
	}
	return deliveryCharge
}

// extractAllIndividualCharges extracts all individual charges from each item for UrbanPiper
func extractAllIndividualCharges(order *orders.Order) []urbanpiperTypes.ItemCharge {
	var allCharges []urbanpiperTypes.ItemCharge

	// Track if we've added totalPackingCharge to avoid duplication
	totalPackingChargeAdded := false

	for _, item := range order.Items {
		// Extract all charges from each item
		for _, charge := range item.Charges {
			// Create individual charge entry for each item with charge details
			allCharges = append(allCharges, urbanpiperTypes.ItemCharge{
				Title: fmt.Sprintf("%s - %s", charge.Title, item.Name),
				Value: charge.Value * float64(item.Quantity),
			})

			// Mark if we found a packing charge
			if strings.ToLower(charge.Title) == "packing charge" || strings.ToLower(charge.Title) == "packing" {
				totalPackingChargeAdded = true
			}
		}
	}

	// If no individual packing charges were found but totalPackingCharge exists, add it
	if !totalPackingChargeAdded && order.OrderInfo.TotalPackingCharge > 0 {
		allCharges = append(allCharges, urbanpiperTypes.ItemCharge{
			Title: "Packing Charge",
			Value: order.OrderInfo.TotalPackingCharge,
		})
	}

	return allCharges
}

// calculateAllIndividualChargesTotal calculates the total value of all individual charges
func calculateAllIndividualChargesTotal(order *orders.Order) float64 {
	allCharges := extractAllIndividualCharges(order)
	total := 0.0

	for _, charge := range allCharges {
		total += charge.Value
	}

	return total
}

// extractIndividualPackingCharges extracts individual packing charges from each item for UrbanPiper
// Kept for backward compatibility
func extractIndividualPackingCharges(order *orders.Order) []urbanpiperTypes.ItemCharge {
	var packingCharges []urbanpiperTypes.ItemCharge

	for itemIndex, item := range order.Items {
		for _, charge := range item.Charges {
			switch strings.ToLower(charge.Title) {
			case "packing charge", "packing":
				// Create individual packing charge entry for each item
				packingCharges = append(packingCharges, urbanpiperTypes.ItemCharge{
					Title: fmt.Sprintf("Packing Charge - %s", item.Name),
					Value: charge.Value * float64(item.Quantity),
				})
			}
		}

		// If no packing charge found in item charges, check if we should allocate from totalPackingCharge
		hasPackingCharge := false
		for _, charge := range item.Charges {
			if strings.ToLower(charge.Title) == "packing charge" || strings.ToLower(charge.Title) == "packing" {
				hasPackingCharge = true
				break
			}
		}

		// If no individual packing charge and this is the first item, allocate totalPackingCharge
		if !hasPackingCharge && itemIndex == 0 && order.OrderInfo.TotalPackingCharge > 0 {
			packingCharges = append(packingCharges, urbanpiperTypes.ItemCharge{
				Title: "Packing Charge",
				Value: order.OrderInfo.TotalPackingCharge,
			})
		}
	}

	return packingCharges
}

// transformFulfillmentModeWithSelfDelivery transforms unified order type to UrbanPiper fulfillment mode including self delivery
func transformFulfillmentModeWithSelfDelivery(orderType string, isSelfDelivery bool) string {
	switch strings.ToLower(orderType) {
	case constants.DeliveryTypeDelivery:
		if isSelfDelivery {
			return constants.UrbanPiperFulfillmentModeDeliverySelf
		}
		return constants.UrbanPiperFulfillmentModeDelivery
	case constants.DeliveryTypeTakeaway:
		return constants.UrbanPiperFulfillmentModePickup
	case constants.DeliveryTypeDineIn:
		return constants.UrbanPiperFulfillmentModePickup // UrbanPiper doesn't have dine-in, map to pickup
	default:
		return constants.UrbanPiperFulfillmentModeDelivery
	}
}

// transformPaymentMode transforms unified payment mode to UrbanPiper payment mode
func transformPaymentMode(mode string) string {
	switch strings.ToLower(mode) {
	case constants.PaymentTypeCash:
		return constants.UrbanPiperPaymentModeCash
	case constants.PaymentTypeCard, constants.PaymentTypeUPI, constants.PaymentTypeOnline:
		return constants.UrbanPiperPaymentModeOnline
	default:
		return constants.UrbanPiperPaymentModeOnline
	}
}

// calculateItemLevelCharges calculates total charges from all items
func calculateItemLevelCharges(items []orders.OrderItem) float64 {
	total := 0.0
	for _, item := range items {
		for _, charge := range item.Charges {
			total += charge.Value * float64(item.Quantity)
		}
	}
	return total
}

// calculateItemLevelTaxes calculates total taxes from all items
func calculateItemLevelTaxes(items []orders.OrderItem) float64 {
	total := 0.0
	for _, item := range items {
		// Calculate taxes from item level
		for _, tax := range item.Taxes {
			total += tax.Value * float64(item.Quantity)
		}
		// Add taxes from variants and add-ons (simplified since nested taxes were removed)
		total += calculateVariantsTaxes(item.Variants, item.Quantity)
		total += calculateAddOnTaxes(item.AddOns, item.Quantity)
	}
	return total
}

// transformUrbanPiperVariantStruct transforms a unified variant to UrbanPiper variant format with nested support
func transformUrbanPiperVariantStruct(variant *orders.Variant) urbanpiperTypes.ItemVariant {
	if variant == nil {
		return urbanpiperTypes.ItemVariant{}
	}

	// Transform nested variants
	nestedVariants := []urbanpiperTypes.ItemVariant{}
	for _, nestedVariant := range variant.Variants {
		nestedVariants = append(nestedVariants, transformUrbanPiperVariantStruct(&nestedVariant))
	}

	// Transform nested add-ons
	nestedAddOns := []urbanpiperTypes.ItemAddOn{}
	for _, addon := range variant.AddOns {
		nestedAddOns = append(nestedAddOns, transformUrbanPiperAddOnStruct(&addon))
	}

	return urbanpiperTypes.ItemVariant{
		RefID:        variant.ID,
		Title:        variant.Name,
		PricePerUnit: variant.UnitPrice,
		Variants:     nestedVariants,
		AddOns:       nestedAddOns,
	}
}

// transformUrbanPiperAddOnStruct transforms a unified add-on to UrbanPiper add-on format with nested support
func transformUrbanPiperAddOnStruct(addon *orders.AddOn) urbanpiperTypes.ItemAddOn {
	if addon == nil {
		return urbanpiperTypes.ItemAddOn{}
	}

	return urbanpiperTypes.ItemAddOn{
		RefID:        addon.ID,
		Title:        addon.Name,
		PricePerUnit: addon.UnitPrice,
	}
}

// calculateVariantsTaxes calculates total taxes from array of variants
func calculateVariantsTaxes(variants []orders.Variant, quantity int) float64 {
	total := 0.0
	for _, variant := range variants {
		total += calculateVariantTaxes(&variant, quantity)
	}
	return total
}

// calculateVariantTaxes calculates total taxes from variants recursively
// Since Taxes field was removed from Variant, this function now returns 0
func calculateVariantTaxes(variant *orders.Variant, quantity int) float64 {
	if variant == nil {
		return 0.0
	}

	total := 0.0
	// Taxes field was removed from Variant struct in unified order structure

	// Add taxes from nested variants
	for _, nestedVariant := range variant.Variants {
		total += calculateVariantTaxes(&nestedVariant, quantity)
	}

	// Add taxes from variant's add-ons (but AddOns don't have taxes anymore)
	total += calculateAddOnTaxes(variant.AddOns, quantity)

	return total
}

// calculateAddOnTaxes calculates total taxes from add-ons recursively
// Since Taxes and nested AddOns fields were removed from AddOn, this function now returns 0
func calculateAddOnTaxes(addOns []orders.AddOn, quantity int) float64 {
	// Taxes and nested AddOns fields were removed from AddOn struct in unified order structure
	// This function now returns 0 as AddOns no longer have taxes or nested structure
	return 0.0
}

// transformPrepTimeDetails transforms order prep time details to UrbanPiper format
func transformPrepTimeDetails(order *orders.Order) *urbanpiperTypes.PrepTimeDetails {
	// Default prep time details - these could be made configurable
	return &urbanpiperTypes.PrepTimeDetails{
		PredictedPrepTime:    0, // Default 20 minutes
		MaxIncreaseThreshold: 0, // Default 10 minutes increase
		MaxDecreaseThreshold: 0, // Default 5 minutes decrease
	}
}
