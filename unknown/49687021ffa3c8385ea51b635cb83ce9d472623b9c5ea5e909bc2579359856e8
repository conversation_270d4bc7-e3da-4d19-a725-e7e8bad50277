package main

import (
	"github.com/nutanalabs/pos-gateway/internal/config"

	"github.com/spf13/cobra"

	logger "github.com/roppenlabs/rapido-logger-go"
)

func initCLI() *cobra.Command {
	var cliCmd = &cobra.Command{
		Use:   "pos-gateway",
		Short: "pos-gateway CLI to manage the service",
	}

	cliCmd.AddCommand(startCommand())
	return cliCmd
}

func startCommand() *cobra.Command {
	var startCmd = &cobra.Command{
		Use:   "start",
		Short: "Starts the service",
		Run: func(cmd *cobra.Command, args []string) {
			configFile := "application"
			if len(args) > 0 {
				configFile = args[0]
			}

			configConfig := config.InitConfig(configFile)
			logger.Init(configConfig.Log.Level)

			serverDependencies, _ := InitDependencies()
			serverDependencies.server.Run(serverDependencies.handlers)
		},
	}

	return startCmd
}
