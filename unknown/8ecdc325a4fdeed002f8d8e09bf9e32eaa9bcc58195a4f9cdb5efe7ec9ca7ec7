package log

import (
	"context"
	"fmt"
	"runtime"
)

// WithCaller returns same context after inserting caller details to the existing fields in context.
func WithCaller(ctx context.Context) context.Context {
	pc, file, line, _ := runtime.Caller(1)
	details := runtime.FuncForPC(pc)
	methodName := details.Name()

	caller := fmt.Sprintf("%s:%d %s", file, line, methodName)

	return WithField(
		ctx,
		"caller",
		caller,
	)
}

// WithFields returns same context after inserting given fields to the existing fields in context.
func WithFields(ctx context.Context, fields Fields) context.Context {
	ctxFields := getContextWithFields(ctx)
	for k, v := range fields {
		ctxFields = ctxFields.Add(k, v)
	}

	return context.WithValue(ctx, ctxKey{}, ctxFields)
}

// WithField returns same context after inserting given key value to the existing fields in context.
func WithField(ctx context.Context, key string, value interface{}) context.Context {
	fields := getContextWithFields(ctx)
	return context.WithValue(ctx, ctxKey{}, fields.Add(key, value))
}

// WithTraceId returns new context after inserting trace id as log field.
func WithTraceId(ctx context.Context, traceId string) context.Context {
	ctx = WithField(ctx, logTraceID, traceId)
	return context.WithValue(ctx, ContextTraceID, traceId)
}
