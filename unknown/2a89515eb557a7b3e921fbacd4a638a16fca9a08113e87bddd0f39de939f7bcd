package log

import (
	"context"
	"fmt"
	"slices"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var logger *zap.Logger
var enabledLevel zapcore.Level

func init() {
	Init("info")
}

func Init(level string) {
	enabledLevel = getZapLogLevel(level)

	cfg := zap.NewProductionConfig()
	cfg.DisableCaller = true
	cfg.Level = zap.NewAtomicLevelAt(enabledLevel)
	cfg.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	logger, _ = cfg.Build(zap.AddCallerSkip(1))
}

func getZapLogLevel(level string) zapcore.Level {
	switch strings.ToLower(level) {
	case "debug":
		return zap.DebugLevel
	case "info":
		return zap.InfoLevel
	case "warn":
		return zap.WarnLevel
	case "error":
		return zap.ErrorLevel
	default:
		return zap.InfoLevel
	}
}

type ctxKey struct{}

const ContextTraceID = "RAPIDO_TRACE_ID"

type Fields map[string]interface{}
type F = Fields

const logTraceID string = "trace_id"

func disabled(level zapcore.Level) bool {
	return !enabledLevel.Enabled(level)
}

func Context() context.Context {
	return context.Background()
}

func getContextWithFields(ctx context.Context) *CtxField {
	if fields, ok := ctx.Value(ctxKey{}).(*CtxField); ok {
		return fields
	}
	return nil
}

func GetTraceID(ctx context.Context) string {
	traceId, _ := ctx.Value(ContextTraceID).(string)
	return traceId
}

func getLastFields(args []any) ([]any, F) {
	if len(args) == 0 {
		return args, nil
	}

	last := args[len(args)-1]
	if fields, ok := last.(F); ok {
		return args[:len(args)-1], fields
	}

	return args, nil
}

func zapIt(ctx context.Context, logFields Fields) []zap.Field {
	ctxFields := getContextWithFields(ctx)

	zapFields := make([]zap.Field, 0, 4+len(logFields))

	for ctxFields != nil {
		zapFields = append(zapFields, ctxFields.Field)
		ctxFields = ctxFields.Prev
	}
	slices.Reverse(zapFields)

	for key, val := range logFields {
		zapFields = append(zapFields, zap.Any(key, val))
	}

	return zapFields
}

func getMessage(template string, fmtArgs []interface{}) string {
	if len(fmtArgs) == 0 {
		return template
	}

	if template != "" {
		return fmt.Sprintf(template, fmtArgs...)
	}

	if len(fmtArgs) == 1 {
		if str, ok := fmtArgs[0].(string); ok {
			return str
		}
	}
	return fmt.Sprint(fmtArgs...)
}

func Debug(ctx context.Context, message string, fields Fields) {
	if disabled(zapcore.DebugLevel) {
		return
	}
	logger.Debug(message, zapIt(ctx, fields)...)
}

func Debugf(ctx context.Context, format string, args ...interface{}) {
	if disabled(zapcore.DebugLevel) {
		return
	}
	args, fields := getLastFields(args)
	logger.Debug(getMessage(format, args), zapIt(ctx, fields)...)
}

func Info(ctx context.Context, message string, fields Fields) {
	if disabled(zapcore.InfoLevel) {
		return
	}
	logger.Info(message, zapIt(ctx, fields)...)
}

func Infof(ctx context.Context, format string, args ...interface{}) {
	if disabled(zapcore.InfoLevel) {
		return
	}
	args, fields := getLastFields(args)
	logger.Info(getMessage(format, args), zapIt(ctx, fields)...)
}

func Warn(ctx context.Context, message string, fields Fields) {
	if disabled(zapcore.WarnLevel) {
		return
	}
	logger.Warn(message, zapIt(ctx, fields)...)
}

func Warnf(ctx context.Context, format string, args ...interface{}) {
	if disabled(zapcore.WarnLevel) {
		return
	}
	args, fields := getLastFields(args)
	logger.Warn(getMessage(format, args), zapIt(ctx, fields)...)
}

func Error(ctx context.Context, message string, fields Fields) {
	if disabled(zapcore.ErrorLevel) {
		return
	}
	logger.Error(message, zapIt(ctx, fields)...)
}

func Errorf(ctx context.Context, format string, args ...interface{}) {
	if disabled(zapcore.ErrorLevel) {
		return
	}
	args, fields := getLastFields(args)
	logger.Error(getMessage(format, args), zapIt(ctx, fields)...)
}

func Fatal(ctx context.Context, message string, fields Fields) {
	// Always log fatal
	logger.Fatal(message, zapIt(ctx, fields)...)
}

func Fatalf(ctx context.Context, format string, args ...interface{}) {
	// Always log fatal
	args, fields := getLastFields(args)
	logger.Fatal(getMessage(format, args), zapIt(ctx, fields)...)
}
