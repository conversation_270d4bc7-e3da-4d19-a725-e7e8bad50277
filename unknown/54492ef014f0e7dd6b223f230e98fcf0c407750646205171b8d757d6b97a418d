package urbanpiper

type RiderStatusRequest struct {
	PlatformOrderID string      `json:"platform_order_id"`
	Status          string      `json:"status"` // Enum: DELIVERED, AT-STORE, NO-RIDER-FOUND, OUT-FOR-DELIVERY, ASSIGNED, UNASSIGNED, CANCELLED
	RiderData       []RiderInfo `json:"rider_data"`
}

type RiderInfo struct {
	RiderName        string `json:"rider_name"`
	RiderPhoneNumber string `json:"rider_phone_number"`
}
