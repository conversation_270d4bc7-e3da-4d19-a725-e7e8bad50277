package urbanpiper

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

func TransformOrderFromTransformer(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to UrbanPiper format using struct-based approach
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.UrbanpiperClientId)

	return transformedOrder
}

// TransformRiderFromTransformer transforms unified rider to UrbanPiper rider format using centralized transformation
func TransformRiderFromTransformer(rider orders.Rider) interface{} {
	// TODO: Implement UrbanPiper rider transformation
	return rider
}
