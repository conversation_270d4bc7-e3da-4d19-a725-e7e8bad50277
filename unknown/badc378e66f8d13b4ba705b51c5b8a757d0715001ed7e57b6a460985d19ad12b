package utils

import (
	"encoding/json"

	logger "github.com/roppenlabs/rapido-logger-go"
)

// convert interface to json
func ConvertInterfaceToJSON(data interface{}) string {
	byteData, err := json.Marshal(data)
	if err != nil {
		logger.Debug(logger.Format{
			Message: "Error in converting interface to json",
		})
		return ""
	}
	return string(byteData)
}

// unmarshal json to interface
func UnmarshalJSONToInterface(data interface{}, v interface{}) error {
	// marshall the data to json
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	err = json.Unmarshal(jsonData, v)
	if err != nil {
		return err
	}
	return nil
}

// slice contains with template type
func SliceContains[T comparable](slice []T, item T) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}
