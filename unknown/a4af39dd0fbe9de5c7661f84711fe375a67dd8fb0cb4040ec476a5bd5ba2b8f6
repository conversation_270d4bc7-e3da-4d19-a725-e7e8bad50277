package petpooja

import (
	"net/http"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// Client represents the Petpooja API client
type Client struct {
	config      *config.Config
	httpClient  *http.Client
	utilsClient utils.HTTPClient
}

// NewClient creates a new Petpooja API client
func NewClient(cfg *config.Config, httpClient *http.Client,
	utilsClient utils.HTTPClient) *Client {
	return &Client{
		config:      cfg,
		httpClient:  httpClient,
		utilsClient: utilsClient,
	}
}

// SendOrder sends order details to Petpooja API
func (c *Client) SendOrder(order interface{}) (interface{}, error) {
	// For now, this is a mock implementation since we don't have the actual Petpooja API URL configured
	// In a real implementation, you would configure the Petpooja API URL in the config file
	// and make the actual HTTP request here

	// Mock successful response
	response := map[string]interface{}{
		"status":  "success",
		"message": "Order sent to <PERSON>pooja successfully",
	}

	return response, nil

	// Commented out the actual HTTP call until proper URL is configured:
	/*
		petpoojaURL := "https://api.petpooja.com/v1/orders" // This should come from config
		res, err := c.utilsClient.Post(utils.HTTPPayload{
			Client:  c.httpClient,
			URL:     petpoojaURL,
			Body:    order,
			Timeout: time.Second * 30,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	*/
}
