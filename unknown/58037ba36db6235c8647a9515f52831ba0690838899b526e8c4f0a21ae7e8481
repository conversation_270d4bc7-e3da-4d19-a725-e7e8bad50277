package utils

import (
	"time"

	"github.com/google/uuid"
)

type Utils interface {
	GenerateUUID() string
	GenerateUnixTimestamp(layout string, timeStr string) int64
	CurrentTime() time.Time
}

type utilsImpl struct{}

func NewUtils() Utils {
	return &utilsImpl{}
}

// GenerateUUID generates a new UUID
func (u utilsImpl) GenerateUUID() string {
	return uuid.New().String()
}

func (u utilsImpl) GenerateUnixTimestamp(layout string, timeStr string) int64 {
	// Load IST timezone
	loc, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		panic(err)
	}

	// Correct the layout to match the format of timeStr
	//layout = "2006-01-02 15:04"

	// Parse the time in IST
	t, err := time.ParseInLocation(layout, timeStr, loc)
	if err != nil {
		panic(err)
	}

	// Get Unix timestamp
	return t.Unix()
}
func (u utilsImpl) CurrentTime() time.Time {
	return time.Now()
}
