package service

import (
	"fmt"
	"github.com/nutanalabs/pos-gateway/internal/posclients"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/repository"
	restaurantsapi "github.com/nutanalabs/pos-gateway/internal/serviceclients/restaurants-api"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
	"go.mongodb.org/mongo-driver/bson"
)

type Service interface {
	SendOrder()
	PushMenu(request types.ServiceRequest) (types.SuccessResponse, error)
	PushItemInventoryStatus(request types.ServiceRequest) (types.SuccessResponse, error)
	PushStoreStatus(request types.ServiceRequest) (types.SuccessResponse, error)
	PushOrder(order *orders.Order, clientId string) (types.SuccessResponse, error)
}

type serviceImpl struct {
	cfg                 *config.Config
	posClientService    posclients.POSClientService
	restaurantAPIClient restaurantsapi.RestaurantAPIClient
	kafkRepo            repository.KafkaRepoInterface
	mongoRepository     repository.MongoRepository
	transformers        transformers.Transformers
}

func NewService(cfg *config.Config, posClientService posclients.POSClientService,
	kafkRepo repository.KafkaRepoInterface,
	restaurantAPIClient restaurantsapi.RestaurantAPIClient,
	mongoRepository repository.MongoRepository) Service {
	utilsImpl := utils.NewUtils()
	service := &serviceImpl{
		cfg:                 cfg,
		posClientService:    posClientService,
		kafkRepo:            kafkRepo,
		restaurantAPIClient: restaurantAPIClient,
		mongoRepository:     mongoRepository,
		transformers:        transformers.NewTransformers(utilsImpl),
	}
	return service
}

func (s serviceImpl) SendOrder() {
	s.posClientService.SendOrder(types.Provider{
		Name: constants.PetpoojaClientId,
		Id:   "808312",
	}, orders.Order{})
}

func (s serviceImpl) PushMenu(request types.ServiceRequest) (types.SuccessResponse, error) {
	// push the raw menu to mongo
	filer, err := GetRestaurantIdFilterFromPosMenu(request.ClientID, request.RawData)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while getting restaurant ID filter from POS menu",
			Data: map[string]string{
				"provider": request.ClientID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}
	err = s.mongoRepository.PushPOSMenu(filer, *request.RawData, request.ClientID)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while pushing menu to mongo",
			Data: map[string]string{
				"menu":     utils.ConvertInterfaceToJSON(request.RawData),
				"provider": request.ClientID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}

	unifiedMenu := request.GetUnifiedMenu()
	if unifiedMenu == nil {
		return types.SuccessResponse{}, fmt.Errorf("invalid unified menu data")
	}

	_, err = s.restaurantAPIClient.PushMenu(unifiedMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while pushing menu to restaurant API",
			Data: map[string]string{
				"provider": request.ClientID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "success",
	}, nil
}

func (s serviceImpl) PushItemInventoryStatus(request types.ServiceRequest) (types.SuccessResponse, error) {
	unifiedInventory := request.GetUnifiedInventory()
	if unifiedInventory == nil {
		return types.SuccessResponse{}, fmt.Errorf("invalid unified inventory data")
	}

	_, err := s.restaurantAPIClient.PushInventory(unifiedInventory)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while pushing inventory to restaurant API",
			Data: map[string]string{
				"provider": request.ClientID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "success",
	}, nil
}

func (s serviceImpl) PushStoreStatus(request types.ServiceRequest) (types.SuccessResponse, error) {
	unifiedStoreStatus := request.GetUnifiedStoreStatus()
	if unifiedStoreStatus == nil {
		return types.SuccessResponse{}, fmt.Errorf("invalid unified store status data")
	}

	_, err := s.restaurantAPIClient.PushStoreStatus(unifiedStoreStatus)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while pushing store status to restaurant API",
			Data: map[string]string{
				"provider": request.ClientID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "success",
	}, nil
}

func (s serviceImpl) PushOrder(order *orders.Order, clientId string) (types.SuccessResponse, error) {
	// Convert unified order to map for storage
	unifiedOrderMap := make(map[string]interface{})
	err := utils.UnmarshalJSONToInterface(order, &unifiedOrderMap)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while converting unified order to map",
			Data: map[string]string{
				"provider": clientId,
				"orderId":  order.OrderInfo.OrderID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}

	// Generate transformed order for the specific provider
	transformedOrder := s.transformers.TransformUnifiedOrderToProviderOrder(order, clientId)

	// Create comprehensive order document with both unified and transformed versions
	comprehensiveOrder := map[string]interface{}{
		"unified_order":     unifiedOrderMap,
		"transformed_order": transformedOrder,
		"provider":          clientId,
		"created_at":        order.OrderInfo.CreatedAt,
		"updated_at":        order.OrderInfo.CreatedAt,
		"order_id":          order.OrderInfo.OrderID,
		"restaurant_id":     order.OrderInfo.RestID,
	}

	// Push the comprehensive order to MongoDB
	err = s.mongoRepository.PushPOSOrder(GetRestaurantIdFilterFromPosOrder(clientId, order), comprehensiveOrder, clientId)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while pushing comprehensive order to mongo",
			Data: map[string]string{
				"provider": clientId,
				"orderId":  order.OrderInfo.OrderID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}

	// Push comprehensive order details to Kafka (both unified and transformed)
	kafkaOrderEvent := map[string]interface{}{
		"event_type":        constants.EventTypeOrderReceived,
		"provider":          clientId,
		"unified_order":     order,
		"transformed_order": transformedOrder,
		"timestamp":         order.OrderInfo.CreatedAt,
		"order_id":          order.OrderInfo.OrderID,
		"restaurant_id":     order.OrderInfo.RestID,
	}

	err = s.kafkRepo.PushOrderDetails(constants.EventTypeOrderReceived, kafkaOrderEvent)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while pushing comprehensive order to kafka",
			Data: map[string]string{
				"provider": clientId,
				"orderId":  order.OrderInfo.OrderID,
				"err":      err.Error(),
			},
		})
		// Don't return error for Kafka failure, just log it
	}

	// Send the order to the POS client service
	provider := types.Provider{
		Name: clientId,
		Id:   order.OrderInfo.RestID,
	}

	_, err = s.posClientService.SendOrder(provider, *order)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while sending order to POS client",
			Data: map[string]string{
				"provider": clientId,
				"orderId":  order.OrderInfo.OrderID,
				"err":      err.Error(),
			},
		})
		return types.SuccessResponse{}, err
	}

	return types.SuccessResponse{
		Message: "Order Details successfully communicated.",
	}, nil
}

// GetRestaurantIdFilterFromPosOrder creates a MongoDB filter for order based on provider and order details
func GetRestaurantIdFilterFromPosOrder(clientId string, order *orders.Order) bson.M {
	switch clientId {
	case constants.PetpoojaClientId:
		return bson.M{
			"orderInfo.orderId": order.OrderInfo.OrderID,
			"provider":          clientId,
		}
	case constants.UrbanpiperClientId:
		return bson.M{
			"orderInfo.orderId": order.OrderInfo.OrderID,
			"provider":          clientId,
		}
	default:
		return bson.M{
			"orderInfo.orderId": order.OrderInfo.OrderID,
			"provider":          clientId,
		}
	}
}
