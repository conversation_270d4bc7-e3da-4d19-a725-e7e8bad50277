# rapido-logger-go
Logger to be used with services written in Go.

#### Sample structure

```
{
    "level": "debug",
    "message": {
        "request_id": "RequestId",
        "customer_id": "",
        "captain_id": "",
        "order_id": "",
        "branch_id": "BranchId",
        "client_id": "ClientId",
        "event": "Event",
        "endpoint": "Endpoint",
        "message": "I am debug",
        "timestamp": "2020-10-05T14:26:42+05:30",
        "data": {
            "one": "one",
            "two": "two"
        }
    }
}
```

#### Installation
Update zshrc/bashrc
```sh
export GOPRIVATE="github.com/roppenlabs"
```

```sh
go get -v github.com/roppenlabs/rapido-logger-go
```

Git config when using different ssh key for rapido
```gitconfig
[url "ssh://**************/roppenlabs"]
    insteadOf = https://github.com/roppenlabs
```

#### Usage

```
package main

import logger "github.com/roppenlabs/rapido-logger-go"

func main() {
	logger.Init("debug")

	msg := logger.Format{
		RequestID: "RequestId",
		BranchID:  "BranchId",
		ClientID:  "ClientId",
		Event:     "Event",
		Endpoint:  "Endpoint",
		Message:   "Message",
		Data: map[string]string{
			"one": "one",
			"two": "two",
		},
	}

	logger.Debug(msg)

}

```

### Some points to take note of

* Always stringify the reference , see examples on how to init logger accordingly (examples folder)
Only pass Stack Trace in an error source object if its absolutely necessary.

* When using V2 logger, setup tracing middleware to generate trace id for every new request
