package utils

import (
	"fmt"
	"time"

	logger "github.com/roppenlabs/rapido-logger-go"
)

type Time interface {
	Now() time.Time
}

type timeImpl struct {
	timeLocationIST *time.Location
}

const (
	ISTTimezoneLocation string  = "Asia/Kolkata"
	SecondsInOneMinute  float64 = 60
)

func NewTime() Time {
	istLocation := getISTTimezoneLocation()
	return &timeImpl{
		timeLocationIST: istLocation,
	}
}

func getISTTimezoneLocation() *time.Location {
	location, err := time.LoadLocation(ISTTimezoneLocation)
	if err != nil {
		logger.Error(logger.Format{
			Message: fmt.Sprintf("Error when fetching time location: %v", err),
		})
		return nil
	}
	return location
}

func (t timeImpl) Now() time.Time {
	return time.Now()
}
