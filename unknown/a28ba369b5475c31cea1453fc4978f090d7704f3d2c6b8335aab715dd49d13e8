package petpooja

import (
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// PetpoojaAdapter implements the POSAdapter interface for Petpooja
type PetpoojaAdapter struct {
	client *Client
}

func (p *PetpoojaAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, url string) (types.SuccessResponse, error) {
	//TODO implement me
	panic("implement me")
}

// NewPetpoojaAdapter creates a new Petpooja adapter instance
func NewPetpoojaAdapter(client *Client) adapter.POSAdapter {
	return &PetpoojaAdapter{
		client: client,
	}
}

// SendOrder sends order details to Petpooja
func (p *PetpoojaAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	transformedOrder := TransformOrder(order)
	_, err := p.client.SendOrder(transformedOrder)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to Petpooja successfully",
	}, nil
}

// SendRiderDetails sends rider details to Petpooja
func (p *PetpoojaAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	//transformedRider := TransformRider(rider)
	//_, err := p.client.SendRiderDetails(transformedRider)
	return types.SuccessResponse{}, nil
}
