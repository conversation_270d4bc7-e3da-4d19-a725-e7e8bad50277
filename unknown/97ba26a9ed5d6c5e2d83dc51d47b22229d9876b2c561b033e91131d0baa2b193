package petpooja

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// TransformOrder transforms unified order to Petpooja order format
func TransformOrder(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to Petpooja format using struct-based approach
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.PetpoojaClientId)

	return transformedOrder
}

// TransformRider transforms unified rider to Petpooja rider format
func TransformRider(rider orders.Rider) interface{} {
	// TODO: Implement Petpooja rider transformation
	return rider
}
