package urbanpiper

// Menu represents the Urban Piper menu structure
type Menu struct {
	OrderBillComponents *OrderBillComponents `json:"order_bill_components"`
	Menu                *MenuData            `json:"menu"`
	Timings             []Timing             `json:"timings"`
	Location            *Location            `json:"location"`
	BillComponents      *BillComponents      `json:"bill_components"`
	CallbackURL         string               `json:"callback_url"`
}

// MenuData represents the menu data
type MenuData struct {
	Categories []Category `json:"categories"`
}

// Category represents a menu category
type Category struct {
	SortOrder     int        `json:"sort_order"`
	ImageURL      *string    `json:"image_url"`
	Subcategories []Category `json:"subcategories"`
	Description   *string    `json:"description"`
	Title         *string    `json:"title"`
	Items         []Item     `json:"items"`
	Timings       *string    `json:"timings"`
	RefID         *string    `json:"ref_id"`
}

// Item represents a menu item
type Item struct {
	Description      *string             `json:"description"`
	NutritionalInfo  *NutritionalInfo    `json:"nutritional_info"`
	Recommended      *bool               `json:"recommended"`
	RefID            *string             `json:"ref_id"`
	CategoryRefID    *string             `json:"category_ref_id"`
	CanonicalID      *string             `json:"canonical_id"`
	ImageURL         *string             `json:"image_url"`
	InStock          *bool               `json:"in_stock"`
	VariantGroups    []VariantGroup      `json:"variant_groups"`
	AddOnGroups      []AddOnGroup        `json:"add_on_groups"`
	FoodType         *int                `json:"food_type"`
	Discounts        []interface{}       `json:"discounts"`
	Price            interface{}         `json:"price"` // Can be string or float64
	Title            *string             `json:"title"`
	FulfillmentModes []string            `json:"fulfillment_modes"`
	Tags             []string            `json:"tags"`
	SubCategoryRefID *string             `json:"sub_category_ref_id"`
	BillComponents   *ItemBillComponents `json:"bill_components"`
	SortOrder        int                 `json:"sort_order"`
}

// ItemBillComponents represents bill components for an item where charges and taxes are IDs
type ItemBillComponents struct {
	Charges []string `json:"charges"`
	Taxes   []string `json:"taxes"`
}

// VariantGroup represents a group of variants
type VariantGroup struct {
	Variants  []Variant `json:"variants"`
	Title     *string   `json:"title"`
	RefID     *string   `json:"ref_id"`
	SortOrder int       `json:"sort_order"`
}

// Variant represents a variant option
type Variant struct {
	RefID            *string          `json:"ref_id"`
	VariantGroups    []VariantGroup   `json:"variant_groups"`
	Price            *float64         `json:"price"`
	Title            *string          `json:"title"`
	InStock          *bool            `json:"in_stock"`
	FoodType         *int             `json:"food_type"`
	AddOnGroups      []AddOnGroup     `json:"add_on_groups"`
	NutritionalInfo  *NutritionalInfo `json:"nutritional_info"`
	FulfillmentModes []string         `json:"fulfillment_modes"`
	SortOrder        int              `json:"sort_order"`
}

// AddOnGroup represents a group of add-ons
type AddOnGroup struct {
	MaximumAllowed *int    `json:"maximum_allowed"`
	Addons         []AddOn `json:"addons"`
	Title          *string `json:"title"`
	MinimumNeeded  *int    `json:"minimum_needed"`
	RefID          *string `json:"ref_id"`
	SortOrder      int     `json:"sort_order"`
}

// AddOn represents an add-on option
type AddOn struct {
	Title            *string          `json:"title"`
	NutritionalInfo  *NutritionalInfo `json:"nutritional_info"`
	InStock          *bool            `json:"in_stock"`
	FoodType         *int             `json:"food_type"`
	RefID            *string          `json:"ref_id"`
	Price            *float64         `json:"price"`
	FulfillmentModes []string         `json:"fulfillment_modes"`
	SortOrder        int              `json:"sort_order"`
	ImageURL         string           `json:"image_url"`
}

// NutritionalInfo represents nutritional information
type NutritionalInfo struct {
	Carbohydrate *NutritionalValue `json:"carbohydrate"`
	Fiber        *NutritionalValue `json:"fiber"`
	Fat          *NutritionalValue `json:"fat"`
	Protein      *NutritionalValue `json:"protein"`
	Calorie      *NutritionalValue `json:"calorie"`
	Sugar        *NutritionalValue `json:"sugar"`
	Sodium       *NutritionalValue `json:"sodium"`
	Cholesterol  *NutritionalValue `json:"cholesterol"`
	Salt         *NutritionalValue `json:"salt"`
}

// NutritionalValue represents a nutritional value with unit
type NutritionalValue struct {
	Unit  *string  `json:"unit"`
	Value *float64 `json:"value"`
}

// Timing represents operating hours
type Timing struct {
	Days  []Day   `json:"days"`
	RefID *string `json:"ref_id"`
}

// Day represents a day's operating hours
type Day struct {
	Slots []Slot  `json:"slots"`
	Day   *string `json:"day"`
}

// Slot represents a time slot
type Slot struct {
	StartTime *string `json:"start_time"`
	EndTime   *string `json:"end_time"`
}

// Location represents location information
type Location struct {
	MinDeliveryTime *string `json:"min_delivery_time"`
	MinPickupTime   *string `json:"min_pickup_time"`
	RefID           *string `json:"ref_id"`
	Timings         *string `json:"timings"`
}

// BillComponents represents bill components
type BillComponents struct {
	Charges []Charge `json:"charges"`
	Taxes   []Tax    `json:"taxes"`
}

// Charge represents a bill charge
type Charge struct {
	Description      *string  `json:"description"`
	Title            *string  `json:"title"`
	FulfillmentModes []string `json:"fulfillment_modes"`
	Value            *float64 `json:"value"`
	RefID            *string  `json:"ref_id"`
	Taxes            []string `json:"taxes"`
	Type             *string  `json:"type"`
}

// Tax represents a tax
type Tax struct {
	Title            *string  `json:"title"`
	Description      *string  `json:"description"`
	Value            *float64 `json:"value"`
	RefID            *string  `json:"ref_id"`
	FulfillmentModes []string `json:"fulfillment_modes"`
}

// OrderBillComponents represents order-level bill components
type OrderBillComponents struct {
	Charges []string `json:"charges"`
}
