package handler

//
//func (h *Handler) Pet<PERSON>ojaRoutes(router *gin.Engine) {
//
//	router.POST("/api/v1/pet-pooja/order-status", func(context *gin.Context) {
//		context.JSON(200, gin.H{
//			"message": "order status updated successfully",
//		})
//	})
//
//	// update store staus
//	router.POST("/api/v1/pet-pooja/store-status", func(context *gin.Context) {
//		context.JSON(200, gin.H{
//			"message": "store status updated successfully",
//		})
//	})
//
//}
