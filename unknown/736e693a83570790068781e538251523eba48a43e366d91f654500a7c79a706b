package server

import (
	"github.com/gin-contrib/pprof"
	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/health"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler"
	logger "github.com/roppenlabs/rapido-logger-go"
)

type Handlers struct {
	HealthHandler    *health.Handler
	MyPackageHandler *handler.Handler
}

func (s *Server) InitRoutes(h Handlers, c *config.Config) {
	router := s.routerGroups.rootRouter
	router.GET("/sanity", h.HealthHandler.CheckSanity)
	router.GET("/health", h.HealthHandler.CheckHealth)

	// Register pprof handlers
	if c.ProfilingEnabled {
		logger.Info(logger.Format{
			Message: "ALERT! Profiling enabled. Please be aware of the performance impact it could have",
		})
		pprof.Register(router)
	}

	h.MyPackageHandler.InitRoutes(router)
}
