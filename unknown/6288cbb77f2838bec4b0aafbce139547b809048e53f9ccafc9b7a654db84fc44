package kafka

import "github.com/stretchr/testify/mock"

type MockKafkaProducer struct {
	mock.Mock
}

func (k *MockKafkaProducer) SendMessage(topic string, message []byte) error {
	ret := k.Called(topic, message)
	return ret.Error(0)
}

func (k *MockKafkaProducer) SendKeyedMessage(topic string, key string, message []byte) error {
	ret := k.Called(topic, key, message)
	return ret.Error(0)
}

func (k *MockKafkaProducer) CheckBrokers() error {
	ret := k.Called()
	return ret.Error(0)
}

func (k *MockKafkaProducer) Close() {
	k.Mock.Called()
}
