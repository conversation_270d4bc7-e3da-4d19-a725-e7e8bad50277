//go:build wireinject
// +build wireinject

package main

import (
	"github.com/google/wire"
	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/dataclients"
	"github.com/nutanalabs/pos-gateway/internal/health"
	"github.com/nutanalabs/pos-gateway/internal/posclients"
	"github.com/nutanalabs/pos-gateway/internal/posgateway"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/repository"
	"github.com/nutanalabs/pos-gateway/internal/server"
	restaurants_api "github.com/nutanalabs/pos-gateway/internal/serviceclients/restaurants-api"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

type ServerDependencies struct {
	config   *config.Config
	server   *server.Server
	handlers server.Handlers
}

func InitDependencies() (ServerDependencies, error) {
	wire.Build(
		wire.Struct(new(ServerDependencies), "*"),
		wire.Struct(new(server.Handlers), "*"),
		server.WireSet,
		posgateway.WireSet,
		health.WireSet,
		config.GetConfig,
		utils.WireSet,
		posclients.WireSet,
		dataclients.WireSet,
		repository.WireSet,
		restaurants_api.WireSet,
		transformers.WireSet,
	)

	return ServerDependencies{}, nil
}
