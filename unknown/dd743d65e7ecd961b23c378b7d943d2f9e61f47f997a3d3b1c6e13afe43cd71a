package main

import (
	logv1 "github.com/roppenlabs/rapido-logger-go"
	"github.com/roppenlabs/rapido-logger-go/v2/log"
)

func main() {
	loggerV2()

	logv1.Info(logv1.Format{
		RequestID:  "some-request-id",
		CustomerID: "test",
		Message:    "V1 Debug log with extra fields",
	})
}

func loggerV2() {
	// Empty context
	ctx := log.Context()

	// ctx = log.WithCaller(ctx)

	log.Infof(ctx, "Without any fields")
	// {"level":"info","ts":"..","msg":"Without any fields"}

	log.Error(ctx, "Error log with extra fields", log.F{"first": 1, "second": 2})

	traceID := "some-trace-id"
	// Add traceID to context
	ctx = log.WithTraceId(ctx, traceID)

	log.Infof(ctx, "Info log with traceID")
	// {"level":"info","ts":"..","msg":"Info log with traceID","trace_id":"some-trace-id"}

	log.Info(ctx, "Info log with extra fields", log.F{"first": 1, "second": 2})
	// {"level":"info","ts":"..","msg":"Info log with extra fields","second":2,"first":1,"trace_id":"some-trace-id"}

	log.Infof(ctx, "Info logf with extra fields", log.F{"first": 1, "second": 2})
	// {"level":"info","ts":"..","msg":"Info log with extra fields","second":2,"first":1,"trace_id":"some-trace-id"}

	log.Infof(ctx, "Info log with format fields: %v", struct{ Test string }{Test: "test"})
	// {"level":"info","ts":"..","msg":"Info log with format fields: {test}","trace_id":"some-trace-id"}

	// Add a sigle field to context to auto log everytime
	ctx = log.WithField(ctx, "first", 1)

	// Add a multiple fields to context to auto log everytime
	ctx = log.WithFields(ctx, log.F{"second": 2, "third": 3})

	log.Info(ctx, "All fields are logged here with on adhoc", log.F{"fourth": 4})
	// {"level":"info","ts":"..","msg":"All fields are logged here with on adhoc","fourth":4,"trace_id":"some-trace-id","first":1,"second":2,"third":3}

	log.Infof(ctx, "All fields are logged here except previos adhoc fields")
	// {"level":"info","ts":"..","msg":"All fields are logged here except previos adhoc fields","trace_id":"some-trace-id","first":1,"second":2,"third":3}

	ctx = log.WithField(ctx, "first", 10)

	log.Infof(ctx, "All fields are logged here with multiple fields with same key")
	// {"level":"info","ts":"..","msg":"All fields are logged here with multiple fields with same key","trace_id":"some-trace-id","first":10,"second":2,"third":3,"first":1}
}
