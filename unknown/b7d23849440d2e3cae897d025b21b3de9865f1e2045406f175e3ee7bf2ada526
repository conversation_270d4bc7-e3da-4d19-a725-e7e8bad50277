package repository

import (
	"encoding/json"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/dataclients/kafka"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

type KafkaRepoInterface interface {
	PushMenuDetails(eventType string, menu interface{}) error
	PushOrderDetails(eventType string, order interface{}) error
}

type kafkaRepoImplementation struct {
	kafkaProducer kafka.ProducerInterface
	utils         utils.Utils
}

func (k kafkaRepoImplementation) PushMenuDetails(eventType string, menu interface{}) error {
	data, err := json.Marshal(menu)
	if err != nil {
		return err
	}
	err = k.kafkaProducer.SendMessage(constants.MenuTopic, data)
	if err != nil {
		return err
	}
	return nil
}

func (k kafkaRepoImplementation) PushOrderDetails(eventType string, order interface{}) error {
	data, err := json.Marshal(order)
	if err != nil {
		return err
	}
	err = k.kafkaProducer.SendMessage(constants.OrderTopic, data)
	if err != nil {
		return err
	}
	return nil
}

func NewKafkaRepo(kafkaProducer kafka.ProducerInterface, utils utils.Utils) KafkaRepoInterface {
	kafkaRepo := &kafkaRepoImplementation{
		kafkaProducer: kafkaProducer,
		utils:         utils,
	}
	return kafkaRepo
}
