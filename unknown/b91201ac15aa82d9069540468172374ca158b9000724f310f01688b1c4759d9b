package types

import (
	"github.com/nutanalabs/pos-gateway/internal/types/common"
)

// ServiceRequest represents the request structure passed from handler to service
type ServiceRequest struct {
	RawData     *map[string]interface{} `json:"rawData"`
	UnifiedData interface{}             `json:"unifiedData"`
	ClientID    string                  `json:"clientId"`
}

// GetUnifiedMenu returns the unified menu data with type assertion
func (s *ServiceRequest) GetUnifiedMenu() *common.UnifiedMenu {
	if menu, ok := s.UnifiedData.(*common.UnifiedMenu); ok {
		return menu
	}
	return nil
}

// GetUnifiedInventory returns the unified inventory data with type assertion
func (s *ServiceRequest) GetUnifiedInventory() *common.InventoryUpdate {
	if inventory, ok := s.UnifiedData.(*common.InventoryUpdate); ok {
		return inventory
	}
	return nil
}

// GetUnifiedStoreStatus returns the unified store status data with type assertion
func (s *ServiceRequest) GetUnifiedStoreStatus() *common.StoreStatus {
	if status, ok := s.UnifiedData.(*common.StoreStatus); ok {
		return status
	}
	return nil
}
