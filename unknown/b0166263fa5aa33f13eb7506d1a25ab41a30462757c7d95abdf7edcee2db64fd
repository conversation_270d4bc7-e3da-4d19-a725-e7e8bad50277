package rlogger

import (
	"time"

	"github.com/roppenlabs/rapido-logger-go/v2/log"
	"go.uber.org/zap"
)

/*
Format is the contract of the Log. Use data field to populate dynamic fields.
*/
type Format struct {
	RequestID  string
	CustomerID string
	CaptainID  string
	OrderID    string
	BranchID   string
	ClientID   string
	Event      string
	Endpoint   string
	Message    string
	Data       map[string]string
}

type RapidoLogger struct {
	*zap.Logger
}

func addIfNonEmpty(key string, value string, result log.F) {
	if value != "" {
		result[key] = value
	}
}

func getZapFields(lfmt Format) log.F {
	result := log.F{}
	addIfNonEmpty("request_id", lfmt.RequestID, result)
	addIfNonEmpty("customer_id", lfmt.CustomerID, result)
	addIfNonEmpty("captain_id", lfmt.CaptainID, result)
	addIfNonEmpty("order_id", lfmt.OrderID, result)
	addIfNonEmpty("branch_id", lfmt.BranchID, result)
	addIfNonEmpty("client_id", lfmt.ClientID, result)
	addIfNonEmpty("event", lfmt.Event, result)
	addIfNonEmpty("endpoint", lfmt.Endpoint, result)
	addIfNonEmpty("timestamp", time.Now().Format(time.RFC3339), result)

	if lfmt.Data != nil {
		result["data"] = lfmt.Data
	}

	return result
}

func Debug(lfmt Format) {
	log.Debug(log.Context(), lfmt.Message, getZapFields(lfmt))
}

func Info(lfmt Format) {
	log.Info(log.Context(), lfmt.Message, getZapFields(lfmt))
}

func Warn(lfmt Format) {
	log.Warn(log.Context(), lfmt.Message, getZapFields(lfmt))
}

func Error(lfmt Format) {
	log.Error(log.Context(), lfmt.Message, getZapFields(lfmt))
}

func Init(logLevel string) {
	log.Init(logLevel)
}
