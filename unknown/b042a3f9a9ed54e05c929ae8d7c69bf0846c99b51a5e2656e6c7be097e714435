package config

// Config struct

type KafkaConfig struct {
	BootstrapServers string
	GroupID          string
	AutoOffsetReset  string
	EnableAutoCommit string
	SessionTimeout   string
}

func (c *Config) GetKafkaConfig() KafkaConfig {
	var conf = KafkaConfig{
		BootstrapServers: c.Kafka.BootstrapServers,
		GroupID:          c.Ka<PERSON>ka.GroupID,
		AutoOffsetReset:  c.<PERSON>.AutoOffsetReset,
		EnableAutoCommit: c.<PERSON>.EnableAutoCommit,
		SessionTimeout:   c.Kafka.SessionTimeout}

	return conf
}
