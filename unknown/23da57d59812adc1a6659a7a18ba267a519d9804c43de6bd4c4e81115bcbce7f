package kafka

import (
	"fmt"
	"github.com/nutanalabs/pos-gateway/internal/config"

	"github.com/confluentinc/confluent-kafka-go/kafka"
	logger "github.com/roppenlabs/rapido-logger-go"
)

type ConsumerInterface interface {
	Close()
	GetConsumer() *kafka.Consumer
}

type Consumer struct {
	consumer *kafka.Consumer
}

func NewKafkaConsumerInstance(config *config.Config) (ConsumerInterface, error) {
	consumer, err := newKafkaConsumer(config)
	return &Consumer{consumer: consumer}, err
}

func newKafkaConsumer(config *config.Config) (*kafka.Consumer, error) {
	kafkaConfig := config.GetKafkaConfig()
	logger.Info(logger.Format{
		Message: "initializing kafka consumer with config",
		Data:    map[string]string{"config": kafkaConfig.BootstrapServers},
	})
	consumerClient, err := kafka.NewConsumer(&kafka.ConfigMap{
		"bootstrap.servers":  kafkaConfig.BootstrapServers,
		"group.id":           kafkaConfig.GroupID,
		"auto.offset.reset":  kafkaConfig.AutoOffsetReset,
		"enable.auto.commit": kafkaConfig.EnableAutoCommit,
		"session.timeout.ms": kafkaConfig.SessionTimeout,
	})
	if err != nil {
		logger.Error(logger.Format{
			Event:   "KAFKA_NEW_CONSUMER_START_FAILED",
			Message: fmt.Sprintf("failed to start new Kafka consumer, with error as: %v", err),
			Data:    map[string]string{"consumer group": kafkaConfig.GroupID},
		})
		return nil, err
	}

	logger.Info(logger.Format{Message: "Successfully connected to kafka as a consumer"})

	return consumerClient, nil

}

func (c *Consumer) GetConsumer() *kafka.Consumer {
	return c.consumer
}

func (c *Consumer) Close() {
	if c.consumer != nil {
		consumerCloseError := c.consumer.Close()
		if consumerCloseError != nil {
			logger.Error(logger.Format{
				Event:   "KAFKA_CLOSE_CONSUMER",
				Message: fmt.Sprintf("failed to close kafka consumer, with error as: %v", consumerCloseError),
			})
		}
	}
}
